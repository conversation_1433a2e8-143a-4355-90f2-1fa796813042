<?php
/**
 * 自动重试机制类
 * 
 * @package Power Importer Pro
 * @version 1.5.2
 */

if ( ! defined( 'ABSPATH' ) ) exit;

class PIP_Auto_Retry {
    
    const MAX_RETRIES = 3;              // 最大重试次数
    const RETRY_DELAY = 300;            // 重试延迟（5分钟）
    const HEARTBEAT_INTERVAL = 60;      // 心跳检查间隔（1分钟）
    
    /**
     * 初始化自动重试机制
     */
    public static function init() {
        // 注册心跳检查
        add_action('init', [__CLASS__, 'schedule_heartbeat_check']);
        
        // 注册心跳检查回调
        add_action('pip_heartbeat_check', [__CLASS__, 'check_stalled_jobs']);
        
        // 注册自动重试回调
        add_action('pip_auto_retry_job', [__CLASS__, 'retry_failed_job']);
    }
    
    /**
     * 调度心跳检查
     */
    public static function schedule_heartbeat_check() {
        if (!wp_next_scheduled('pip_heartbeat_check')) {
            wp_schedule_event(time(), 'pip_heartbeat', 'pip_heartbeat_check');
        }
    }
    
    /**
     * 注册自定义时间间隔
     */
    public static function add_cron_intervals($schedules) {
        $schedules['pip_heartbeat'] = [
            'interval' => self::HEARTBEAT_INTERVAL,
            'display' => __('Power Importer Heartbeat', 'power-importer-pro')
        ];
        return $schedules;
    }
    
    /**
     * 检查卡住的任务
     */
    public static function check_stalled_jobs() {
        // 检查是否启用自动重试
        if (!get_option('pip_enable_auto_retry', true)) {
            return;
        }
        
        global $wpdb;
        
        $jobs_table = $wpdb->prefix . 'pip_import_jobs';
        
        // 查找运行超过30分钟的任务
        $stalled_jobs = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$jobs_table} 
            WHERE status = 'running' 
            AND started_at < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
            AND (retry_count IS NULL OR retry_count < %d)
        ", self::MAX_RETRIES));
        
        foreach ($stalled_jobs as $job) {
            self::handle_stalled_job($job);
        }
    }
    
    /**
     * 处理卡住的任务
     */
    private static function handle_stalled_job($job) {
        $retry_count = intval($job->retry_count ?? 0);
        
        if ($retry_count >= self::MAX_RETRIES) {
            // 超过最大重试次数，标记为失败
            pip_db()->update_job($job->id, [
                'status' => 'failed',
                'finished_at' => current_time('mysql', 1)
            ]);
            
            pip_db()->add_log($job->id, 
                "❌ 任务超过最大重试次数({$retry_count})，标记为失败", 
                'ERROR'
            );
            
            return;
        }
        
        // 增加重试计数
        $new_retry_count = $retry_count + 1;
        
        // 更新任务状态
        pip_db()->update_job($job->id, [
            'status' => 'pending',
            'retry_count' => $new_retry_count,
            'started_at' => null
        ]);
        
        // 调度重试
        as_schedule_single_action(
            time() + self::RETRY_DELAY,
            'pip_auto_retry_job',
            [$job->id],
            'power-importer-pro-retry'
        );
        
        pip_db()->add_log($job->id, 
            "⚠️ 检测到任务卡住，将在5分钟后自动重试 (第{$new_retry_count}次)", 
            'WARNING'
        );
    }
    
    /**
     * 重试失败的任务
     */
    public static function retry_failed_job($job_id) {
        $job = pip_db()->get_job($job_id);
        
        if (!$job || $job->status !== 'pending') {
            return;
        }
        
        pip_db()->add_log($job_id, 
            "🔄 开始自动重试任务 (第{$job->retry_count}次)", 
            'INFO'
        );
        
        // 检查文件大小决定使用哪种处理器
        $file_size_mb = file_exists($job->file_path) ? round(filesize($job->file_path) / 1024 / 1024, 2) : 0;
        $large_file_threshold = get_option('pip_large_file_threshold', 3);
        
        if ($file_size_mb > $large_file_threshold) {
            // 大文件使用分块处理
            as_enqueue_async_action(
                'pip_process_chunk',
                [$job_id],
                'power-importer-pro-group'
            );
        } else {
            // 小文件使用快速处理
            as_enqueue_async_action(
                'pip_process_import_file',
                [$job_id],
                'power-importer-pro-group'
            );
        }
    }
    
    /**
     * 手动重试任务
     */
    public static function manual_retry($job_id) {
        $job = pip_db()->get_job($job_id);
        
        if (!$job) {
            return false;
        }
        
        // 重置重试计数和状态
        pip_db()->update_job($job_id, [
            'status' => 'pending',
            'retry_count' => 0,
            'started_at' => null,
            'finished_at' => null,
            'processed_rows' => 0
        ]);
        
        // 清理处理状态
        delete_option("pip_chunk_state_{$job_id}");
        
        pip_db()->add_log($job_id, "🔄 手动重试任务", 'INFO');
        
        // 立即调度
        $file_size_mb = file_exists($job->file_path) ? round(filesize($job->file_path) / 1024 / 1024, 2) : 0;
        $large_file_threshold = get_option('pip_large_file_threshold', 3);
        
        if ($file_size_mb > $large_file_threshold) {
            return as_enqueue_async_action('pip_process_chunk', [$job_id], 'power-importer-pro-group');
        } else {
            return as_enqueue_async_action('pip_process_import_file', [$job_id], 'power-importer-pro-group');
        }
    }
    
    /**
     * 获取任务重试信息
     */
    public static function get_retry_info($job_id) {
        $job = pip_db()->get_job($job_id);
        
        if (!$job) {
            return null;
        }
        
        $retry_count = intval($job->retry_count ?? 0);
        $max_retries = self::MAX_RETRIES;
        
        return [
            'retry_count' => $retry_count,
            'max_retries' => $max_retries,
            'can_retry' => $retry_count < $max_retries,
            'next_retry_in' => $job->status === 'pending' ? self::get_next_retry_time($job_id) : null
        ];
    }
    
    /**
     * 获取下次重试时间
     */
    private static function get_next_retry_time($job_id) {
        $next_action = as_get_scheduled_actions([
            'hook' => 'pip_auto_retry_job',
            'args' => [$job_id],
            'status' => 'pending'
        ]);
        
        if (!empty($next_action)) {
            $action = reset($next_action);
            return $action->get_schedule()->get_date()->getTimestamp() - time();
        }
        
        return null;
    }
    
    /**
     * 取消自动重试
     */
    public static function cancel_auto_retry($job_id) {
        $actions = as_get_scheduled_actions([
            'hook' => 'pip_auto_retry_job',
            'args' => [$job_id],
            'status' => 'pending'
        ], 'ids');
        
        foreach ($actions as $action_id) {
            as_cancel_action($action_id);
        }
        
        pip_db()->add_log($job_id, "🚫 已取消自动重试", 'INFO');
    }
}

// 初始化自动重试机制
add_action('plugins_loaded', ['PIP_Auto_Retry', 'init']);
add_filter('cron_schedules', ['PIP_Auto_Retry', 'add_cron_intervals']);