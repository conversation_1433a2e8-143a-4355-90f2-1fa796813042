#!/bin/bash

# ========== 配置区 ==========
MOTHER_DOMAIN="astra.nestlyalli.shop"      # 母站域名 (旧的，用于替换)
MOTHER_DIR="/www/wwwroot/${MOTHER_DOMAIN}"   # 母站文件根目录
MOTHER_DB_TO_DUMP="astra_nestlyalli"     # 实际要 dump 的母站数据库名
OLD_ADMIN_EMAIL="<EMAIL>" # 母站的管理员邮箱，用于替换

MYSQL_ROOT_USER="root"
# !!! 直接硬编码 MySQL Root 密码 - 仅限一次性或高度受控环境 !!!
MYSQL_ROOT_PASS="2KHNS4XSsMBLZJ7B"
# !!! 操作完成后请务必删除此脚本或移除密码 !!!

WP_CLI_USER="www" # 网站文件运行用户
WP_CLI_PATH="/usr/local/bin/wp" # WP-CLI路径
# ===========================

NEW_DOMAIN=$1
if [ -z "$NEW_DOMAIN" ]; then
  echo "用法: $0 newsite.com"
  exit 1
fi

log_message() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - [${NEW_DOMAIN}] - $1"
}

error_exit() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - [${NEW_DOMAIN}] - $1" >&2
    return 1
}

# --- 使用硬编码的 MySQL Root 密码 ---
MYSQL_CMD_AUTH_PART="-u${MYSQL_ROOT_USER} -p'${MYSQL_ROOT_PASS}'"
# --- END 使用硬编码的 MySQL Root 密码 ---


echo "=============================="
log_message "开始为 ${NEW_DOMAIN} 执行核心克隆任务 (使用WP-CLI配置config, 硬编码root密码)"
NEW_SITE_ROOT_DIR="/www/wwwroot/${NEW_DOMAIN}"
NEW_ADMIN_EMAIL="sale@${NEW_DOMAIN}" # 新站点的管理员邮箱

# --- 步骤 1: 检查网站根目录 ---
# ... (与之前脚本相同) ...
log_message "步骤 1: 检查网站根目录 ${NEW_SITE_ROOT_DIR}"
if [ ! -d "${NEW_SITE_ROOT_DIR}" ]; then
    error_exit "前提条件未满足: 网站根目录 ${NEW_SITE_ROOT_DIR} 不存在！请先通过宝塔或手动创建站点及目录。"
fi
log_message "清理网站根目录 ${NEW_SITE_ROOT_DIR} 中的现有文件 (如果有)..."
sudo find "${NEW_SITE_ROOT_DIR}" -mindepth 1 -delete

# --- 步骤 2: 创建数据库和用户 ---
# ... (与之前脚本相同，但 MYSQL_CMD_EXEC 会使用硬编码的密码) ...
NEW_DB_NAME_RAW=$(echo "${NEW_DOMAIN//./_}" | tr '-' '_')
NEW_DB_NAME="${NEW_DB_NAME_RAW%_com}_com"
if [[ ! "$NEW_DB_NAME" == *"_com"* ]]; then
    NEW_DB_NAME="${NEW_DB_NAME_RAW}_com"
fi
NEW_DB_USER="${NEW_DB_NAME}"
NEW_DB_PASS=$(openssl rand -base64 12)

log_message "步骤 2: 创建数据库 ${NEW_DB_NAME} 和用户 ${NEW_DB_USER}"
MYSQL_CMD_EXEC="mysql ${MYSQL_CMD_AUTH_PART}"

eval "${MYSQL_CMD_EXEC}" -e "DROP DATABASE IF EXISTS \`${NEW_DB_NAME}\`;"
eval "${MYSQL_CMD_EXEC}" -e "CREATE DATABASE \`${NEW_DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if [ $? -ne 0 ]; then error_exit "创建数据库 ${NEW_DB_NAME} 失败。"; fi

eval "${MYSQL_CMD_EXEC}" -e "DROP USER IF EXISTS '${NEW_DB_USER}'@'localhost';"
eval "${MYSQL_CMD_EXEC}" -e "CREATE USER '${NEW_DB_USER}'@'localhost' IDENTIFIED BY '${NEW_DB_PASS}';"
if [ $? -ne 0 ]; then error_exit "创建用户 ${NEW_DB_USER} 失败。"; fi
eval "${MYSQL_CMD_EXEC}" -e "GRANT ALL PRIVILEGES ON \`${NEW_DB_NAME}\`.* TO '${NEW_DB_USER}'@'localhost';"
if [ $? -ne 0 ]; then error_exit "为用户 ${NEW_DB_USER} 授权失败。"; fi
eval "${MYSQL_CMD_EXEC}" -e "FLUSH PRIVILEGES;"


# --- 步骤 3: 复制母站所有文件 (包括 wp-config.php) ---
# ... (与之前脚本相同) ...
log_message "步骤 3: 复制母站所有文件到 ${NEW_SITE_ROOT_DIR}"
if [ ! -d "${MOTHER_DIR}" ]; then
    error_exit "母站目录 ${MOTHER_DIR} 不存在，请检查！";
fi
sudo rsync -avz --exclude=".user.ini" --exclude="nginx_error.log" --exclude="error_log" "${MOTHER_DIR}/" "${NEW_SITE_ROOT_DIR}/"
if [ $? -ne 0 ]; then error_exit "复制母站文件失败。"; fi

log_message "设置文件权限..."
sudo chown -R ${WP_CLI_USER}:${WP_CLI_USER} "${NEW_SITE_ROOT_DIR}"
sudo find "${NEW_SITE_ROOT_DIR}" -type d -exec chmod 755 {} \;
sudo find "${NEW_SITE_ROOT_DIR}" -type f -exec chmod 644 {} \;


# --- 步骤 4: 使用 WP-CLI 配置新的 wp-config.php ---
# ... (与之前脚本相同) ...
log_message "步骤 4: 使用 WP-CLI 配置新的 wp-config.php"
WP_CONFIG_FILE="${NEW_SITE_ROOT_DIR}/wp-config.php"
WP_CONFIG_COMMAND="sudo -u ${WP_CLI_USER} ${WP_CLI_PATH} --path=${NEW_SITE_ROOT_DIR} config"

if [ ! -f "${WP_CONFIG_FILE}" ]; then
    log_message "警告: 母站的 wp-config.php 未复制或不存在于 ${WP_CONFIG_FILE}。"
    log_message "尝试从 wp-config-sample.php 创建新的 wp-config.php..."
    if [ -f "${NEW_SITE_ROOT_DIR}/wp-config-sample.php" ]; then
        sudo cp "${NEW_SITE_ROOT_DIR}/wp-config-sample.php" "${WP_CONFIG_FILE}"
        sudo chown ${WP_CLI_USER}:${WP_CLI_USER} "${WP_CONFIG_FILE}"
    else
        error_exit "wp-config-sample.php 也不存在，无法配置 wp-config.php。"
    fi
fi

log_message "设置数据库连接信息..."
${WP_CONFIG_COMMAND} set DB_NAME "${NEW_DB_NAME}" --raw
if [ $? -ne 0 ]; then error_exit "WP-CLI 设置 DB_NAME 失败。"; fi
${WP_CONFIG_COMMAND} set DB_USER "${NEW_DB_USER}" --raw
if [ $? -ne 0 ]; then error_exit "WP-CLI 设置 DB_USER 失败。"; fi
${WP_CONFIG_COMMAND} set DB_PASSWORD "${NEW_DB_PASS}" --type=string
if [ $? -ne 0 ]; then error_exit "WP-CLI 设置 DB_PASSWORD 失败。"; fi
${WP_CONFIG_COMMAND} set DB_HOST "localhost" --raw
if [ $? -ne 0 ]; then error_exit "WP-CLI 设置 DB_HOST 失败。"; fi

log_message "重新生成并设置安全密钥和盐值..."
${WP_CONFIG_COMMAND} salt regenerate --quiet
if [ $? -ne 0 ]; then error_exit "WP-CLI 生成安全密钥失败。"; fi

log_message "插入 HTTPS 强制设置到 wp-config.php..."
HTTPS_SETTINGS_TO_INSERT='
$_SERVER["HTTPS"] = "on";
define("FORCE_SSL_LOGIN", true);
define("FORCE_SSL_ADMIN", true);
'
ANCHOR_LINE_PATTERN="/\/\* That's all, stop editing! Happy publishing\. \*\//"
TEMP_CONFIG_FILE="${WP_CONFIG_FILE}.tmp_awk"
sudo awk -v settings="${HTTPS_SETTINGS_TO_INSERT}" -v anchor="${ANCHOR_LINE_PATTERN}" '
    found_https_server = 0; found_force_ssl_login = 0; found_force_ssl_admin = 0;
    {
        skip_line = 0;
        if ($0 ~ /\$_SERVER\["HTTPS"\] = "on";/) { found_https_server = 1; skip_line = 1; }
        if ($0 ~ /define\("FORCE_SSL_LOGIN", true\);/) { found_force_ssl_login = 1; skip_line = 1; }
        if ($0 ~ /define\("FORCE_SSL_ADMIN", true\);/) { found_force_ssl_admin = 1; skip_line = 1; }
        if (NR == FNR && $0 ~ anchor) { insert_before_line = NR; }
        if (NR != FNR && skip_line == 0) { print $0; }
        if (NR != FNR && NR == insert_before_line) { printf "%s\n", settings; }
    }
' "${WP_CONFIG_FILE}" "${WP_CONFIG_FILE}" > "${TEMP_CONFIG_FILE}"
if [ -s "${TEMP_CONFIG_FILE}" ]; then
    sudo mv "${TEMP_CONFIG_FILE}" "${WP_CONFIG_FILE}"
else
    log_message "警告: 使用 awk 修改 wp-config.php (HTTPS设置) 失败，临时文件为空。请检查。"
    rm -f "${TEMP_CONFIG_FILE}"
fi
sudo chmod 600 "${WP_CONFIG_FILE}"
sudo chown ${WP_CLI_USER}:${WP_CLI_USER} "${WP_CONFIG_FILE}"


# --- 步骤 5: 复制母站数据库内容到新数据库 ---
# ... (与之前脚本相同，但 MYSQLDUMP_CMD_EXEC 会使用硬编码的密码) ...
log_message "步骤 5: 复制数据库内容从 ${MOTHER_DB_TO_DUMP} 到 ${NEW_DB_NAME}"
TEMP_SQL_FILE="/tmp/${MOTHER_DB_TO_DUMP}_dump_$(date +%s)_${RANDOM}.sql"

log_message "导出母站数据库 ${MOTHER_DB_TO_DUMP}..."
MYSQLDUMP_CMD_EXEC="mysqldump ${MYSQL_CMD_AUTH_PART}"
sudo ${MYSQLDUMP_CMD_EXEC} --no-tablespaces --single-transaction --quick --lock-tables=false "${MOTHER_DB_TO_DUMP}" > "${TEMP_SQL_FILE}"
if [ $? -ne 0 ]; then
    rm -f "${TEMP_SQL_FILE}"
    error_exit "导出母站数据库 ${MOTHER_DB_TO_DUMP} 失败。";
fi

log_message "导入数据到新数据库 ${NEW_DB_NAME} (使用新用户 ${NEW_DB_USER})..."
sudo mysql -u"${NEW_DB_USER}" -p"${NEW_DB_PASS}" "${NEW_DB_NAME}" < "${TEMP_SQL_FILE}"
if [ $? -ne 0 ]; then
    rm -f "${TEMP_SQL_FILE}"
    error_exit "导入数据到新数据库 ${NEW_DB_NAME} 失败。";
fi
rm -f "${TEMP_SQL_FILE}"


# --- 步骤 6: 使用 WP-CLI 更新域名和管理员邮箱 ---
# ... (与之前脚本相同) ...
log_message "步骤 6: 使用 WP-CLI 更新数据库中的域名和管理员邮箱"
if ! command -v "${WP_CLI_PATH}" &> /dev/null; then
    error_exit "WP-CLI 未在 ${WP_CLI_PATH} 找到或未安装！";
fi
WP_COMMAND_EXEC="sudo -u ${WP_CLI_USER} ${WP_CLI_PATH} --path=${NEW_SITE_ROOT_DIR}"
TARGET_URL_SCHEME="https"
${WP_COMMAND_EXEC} search-replace "http://${MOTHER_DOMAIN}" "${TARGET_URL_SCHEME}://${NEW_DOMAIN}" --all-tables-with-prefix --skip-columns=guid --report-changed-only --precise
${WP_COMMAND_EXEC} search-replace "https://${MOTHER_DOMAIN}" "${TARGET_URL_SCHEME}://${NEW_DOMAIN}" --all-tables-with-prefix --skip-columns=guid --report-changed-only --precise
log_message "更新站点 URL 和 Home URL 为 ${TARGET_URL_SCHEME}://${NEW_DOMAIN}..."
${WP_COMMAND_EXEC} option update siteurl "${TARGET_URL_SCHEME}://${NEW_DOMAIN}"
${WP_COMMAND_EXEC} option update home "${TARGET_URL_SCHEME}://${NEW_DOMAIN}"
log_message "替换管理员邮箱从 ${OLD_ADMIN_EMAIL} 到 ${NEW_ADMIN_EMAIL} (用户ID 1)..."
ADMIN_USER_EXISTS=$(${WP_COMMAND_EXEC} user get 1 --field=ID --format=ids 2>/dev/null)
if [ -n "$ADMIN_USER_EXISTS" ]; then
    ${WP_COMMAND_EXEC} user update 1 --user_email="${NEW_ADMIN_EMAIL}"
else
    log_message "警告: 管理员用户 (ID 1) 未找到，无法更新邮箱。"
fi
log_message "刷新固定链接规则..."
${WP_COMMAND_EXEC} rewrite flush --hard


# --- 步骤 7: 提示 Nginx 配置和 SSL ---
# ... (与之前脚本相同) ...
log_message "步骤 7: Nginx 配置和 SSL (重要提示)"
echo "--------------------------------------------------------------------------------------"
log_message "此脚本已完成核心克隆任务。"
log_message "您必须确保已为 ${NEW_DOMAIN} 手动或通过宝塔面板配置了正确的 Nginx 虚拟主机，"
log_message "并将其 Web 根目录指向 ${NEW_SITE_ROOT_DIR}。"
log_message "由于您在 wp-config.php 中强制 HTTPS，请确保 Nginx 也配置为监听 443 端口并有有效的 SSL 证书，"
log_message "否则浏览器将显示不安全警告或无法连接。"
echo "--------------------------------------------------------------------------------------"

# --- 完成 ---
log_message "站点 ${NEW_DOMAIN} 核心克隆任务完成！"
log_message "新数据库信息: DB Name: ${NEW_DB_NAME}, DB User: ${NEW_DB_USER}, DB Pass: ${NEW_DB_PASS} (请妥善保管)"
log_message "请验证 Nginx 配置并确保网站 ${TARGET_URL_SCHEME}://${NEW_DOMAIN} 可以访问。"
echo "=============================="
# exit 0