<?php
/**
 * 手动系统清理类
 * 
 * @package Power Importer Pro
 * @version 1.5.1
 */

if ( ! defined( 'ABSPATH' ) ) exit;

class PIP_Manual_Cleanup {
    
    /**
     * 执行手动系统清理
     */
    public function perform_manual_cleanup() {
        $start_time = microtime(true);
        $results = [];
        
        // 1. 🗂️ 清理WooCommerce缓存
        $results[] = $this->cleanup_woocommerce_cache();
        
        // 2. 🔄 重建产品索引
        $results[] = $this->rebuild_product_indexes();
        
        // 3. 🏷️ 优化分类和标签
        $results[] = $this->optimize_taxonomies();
        
        // 4. 🖼️ 清理无效图片引用
        $results[] = $this->cleanup_invalid_images();
        
        // 5. 🧹 清理临时数据
        $results[] = $this->cleanup_temporary_data();
        
        // 6. 🗂️ 清理过期日志
        $results[] = $this->cleanup_old_logs();
        
        $cleanup_time = round((microtime(true) - $start_time) * 1000, 2);
        
        return sprintf(
            __('Manual cleanup completed in %sms. Tasks: %s', 'power-importer-pro'),
            $cleanup_time,
            implode(', ', array_filter($results))
        );
    }
    
    /**
     * 🗂️ 清理WooCommerce缓存
     */
    private function cleanup_woocommerce_cache() {
        // 清理WooCommerce对象缓存
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients();
        }
        
        // 清理产品查询缓存
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // 清理WooCommerce相关的transients
        global $wpdb;
        $deleted = $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wc_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_wc_%'");
        
        return __('Cache cleared', 'power-importer-pro');
    }
    
    /**
     * 🔄 重建产品索引
     */
    private function rebuild_product_indexes() {
        // 重建WooCommerce产品查找表
        if (class_exists('WC_Install')) {
            WC_Install::create_tables();
        }
        
        // 更新产品可见性
        if (function_exists('wc_update_product_lookup_tables')) {
            wc_update_product_lookup_tables();
        }
        
        // 重建产品属性查找表
        if (function_exists('wc_update_product_attributes_lookup_table')) {
            wc_update_product_attributes_lookup_table();
        }
        
        return __('Indexes rebuilt', 'power-importer-pro');
    }
    
    /**
     * 🏷️ 优化分类和标签
     */
    private function optimize_taxonomies() {
        // 更新分类计数
        $taxonomies = ['product_cat', 'product_tag', 'product_brand'];
        foreach ($taxonomies as $taxonomy) {
            if (taxonomy_exists($taxonomy)) {
                wp_update_term_count_now(get_terms($taxonomy, ['fields' => 'ids', 'hide_empty' => false]), $taxonomy);
            }
        }
        
        // 清理空的分类
        $empty_terms = get_terms([
            'taxonomy' => ['product_cat', 'product_tag'],
            'hide_empty' => false,
            'count' => 0,
            'fields' => 'ids'
        ]);
        
        $cleaned = 0;
        if (!empty($empty_terms) && !is_wp_error($empty_terms)) {
            foreach ($empty_terms as $term_id) {
                wp_delete_term($term_id, 'product_cat');
                $cleaned++;
            }
        }
        
        return sprintf(__('Taxonomies optimized (%d empty terms removed)', 'power-importer-pro'), $cleaned);
    }
    
    /**
     * 🖼️ 清理无效图片引用
     */
    private function cleanup_invalid_images() {
        global $wpdb;
        
        // 查找无效的缩略图引用
        $invalid_thumbnails = $wpdb->get_results("
            SELECT pm.post_id, pm.meta_value 
            FROM {$wpdb->postmeta} pm 
            LEFT JOIN {$wpdb->posts} p ON pm.meta_value = p.ID 
            WHERE pm.meta_key = '_thumbnail_id' 
            AND pm.meta_value != '' 
            AND p.ID IS NULL
        ");
        
        $cleaned_count = 0;
        foreach ($invalid_thumbnails as $invalid) {
            delete_post_meta($invalid->post_id, '_thumbnail_id');
            $cleaned_count++;
        }
        
        return sprintf(__('Invalid images cleaned (%d references)', 'power-importer-pro'), $cleaned_count);
    }
    
    /**
     * 🧹 清理临时数据
     */
    private function cleanup_temporary_data() {
        global $wpdb;
        
        // 清理过期的transients
        $deleted_transients = $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_timeout_%' 
            AND option_value < %d
        ", time()));
        
        // 清理孤立的transient数据
        $wpdb->query("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_%' 
            AND option_name NOT IN (
                SELECT CONCAT('_transient_', SUBSTRING(option_name, 20)) 
                FROM {$wpdb->options} 
                WHERE option_name LIKE '_transient_timeout_%'
            )
        ");
        
        // 清理所有分块状态
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'pip_chunk_state_%'");
        
        return sprintf(__('Temporary data cleaned (%d transients)', 'power-importer-pro'), $deleted_transients);
    }
    
    /**
     * 🗂️ 清理过期日志
     */
    private function cleanup_old_logs() {
        $auto_cleanup = get_option('pip_auto_cleanup_logs', true);
        if (!$auto_cleanup) {
            return __('Log cleanup disabled', 'power-importer-pro');
        }
        
        global $wpdb;
        
        $logs_table = $wpdb->prefix . 'pip_import_logs';
        $jobs_table = $wpdb->prefix . 'pip_import_jobs';
        
        // 删除30天前已完成任务的日志
        $deleted_logs = $wpdb->query($wpdb->prepare("
            DELETE l FROM {$logs_table} l
            INNER JOIN {$jobs_table} j ON l.job_id = j.id
            WHERE j.status IN ('completed', 'failed', 'cancelled')
            AND j.finished_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        "));
        
        // 删除30天前已完成的任务记录
        $deleted_jobs = $wpdb->query($wpdb->prepare("
            DELETE FROM {$jobs_table}
            WHERE status IN ('completed', 'failed', 'cancelled')
            AND finished_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        "));
        
        return sprintf(__('Old logs cleaned (%d jobs, %d logs)', 'power-importer-pro'), $deleted_jobs, $deleted_logs);
    }
} 