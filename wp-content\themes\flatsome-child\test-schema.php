<?php
/**
 * Schema Testing Tool
 * 临时测试文件，用于验证结构化数据
 * 使用方法：访问 https://yoursite.com/wp-content/themes/flatsome-child/test-schema.php?product_id=87475
 */

// 防止直接访问
if (!isset($_GET['product_id'])) {
    die('请提供产品ID参数：?product_id=87475');
}

// 加载 WordPress
require_once('../../../wp-load.php');

$product_id = intval($_GET['product_id']);
$product = wc_get_product($product_id);

if (!$product) {
    die('产品不存在');
}

// 设置全局变量
global $post;
$post = get_post($product_id);
setup_postdata($post);

header('Content-Type: application/json; charset=utf-8');

// 模拟结构化数据生成逻辑
$shipping_policy_url = '';
$return_policy_url = '';

// Try WooCommerce settings first
if (function_exists('wc_get_page_id')) {
    $shipping_page_id = wc_get_page_id('shipping_policy');
    $return_page_id = wc_get_page_id('refund_returns');
    
    if ($shipping_page_id && $shipping_page_id > 0) {
        $shipping_policy_url = get_permalink($shipping_page_id);
    }
    
    if ($return_page_id && $return_page_id > 0) {
        $return_policy_url = get_permalink($return_page_id);
    }
}

// Fallback to common page slugs if WooCommerce pages not set
if (empty($shipping_policy_url)) {
    $common_shipping_slugs = ['shipping-policy', 'shipping', 'delivery-policy', 'shipping-info'];
    foreach ($common_shipping_slugs as $slug) {
        $page = get_page_by_path($slug);
        if ($page) {
            $shipping_policy_url = get_permalink($page->ID);
            break;
        }
    }
}

if (empty($return_policy_url)) {
    $common_return_slugs = ['refund-returns', 'return-policy', 'returns', 'refund-policy'];
    foreach ($common_return_slugs as $slug) {
        $page = get_page_by_path($slug);
        if ($page) {
            $return_policy_url = get_permalink($page->ID);
            break;
        }
    }
}

// Allow custom URL override via filters
$custom_shipping_url = apply_filters('flatsome_child_shipping_policy_url', '');
$custom_return_url = apply_filters('flatsome_child_return_policy_url', '');

if (!empty($custom_shipping_url)) {
    $shipping_policy_url = $custom_shipping_url;
}
if (!empty($custom_return_url)) {
    $return_policy_url = $custom_return_url;
}

// Final fallback to default URLs
if (empty($shipping_policy_url)) {
    $shipping_policy_url = home_url('/shipping-policy/');
}
if (empty($return_policy_url)) {
    $return_policy_url = home_url('/refund-returns/');
}

// Build enhanced schema
$schema = array(
    '@context' => 'https://schema.org/',
    '@type' => 'Product',
    '@id' => get_permalink($post->ID) . '#enhanced-product-schema-test',
    'name' => get_the_title($post->ID),
    'description' => wp_strip_all_tags($post->post_excerpt ?: $post->post_content),
    'sku' => $product->get_sku(),
    'productID' => $product->get_id(),
    'url' => get_permalink($post->ID),
    'image' => array(
        '@type' => 'ImageObject',
        'url' => wp_get_attachment_image_url($product->get_image_id(), 'full') ?: false,
        'width' => 800,
        'height' => 800
    ),
    'brand' => array(
        '@type' => 'Brand',
        'name' => get_bloginfo('name')
    ),
    'offers' => array(
        '@type' => 'Offer',
        'price' => $product->get_price(),
        'priceCurrency' => get_woocommerce_currency(),
        'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
        'itemCondition' => 'https://schema.org/NewCondition',
        'priceValidUntil' => date('Y-m-d', strtotime('+1 year')),
        'url' => get_permalink($post->ID),
        'seller' => array(
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'url' => home_url()
        ),
        // Enhanced shipping details
        'shippingDetails' => array(
            '@type' => 'OfferShippingDetails',
            'shippingRate' => array(
                '@type' => 'MonetaryAmount',
                'value' => '0',
                'currency' => get_woocommerce_currency()
            ),
            'shippingDestination' => array(
                '@type' => 'DefinedRegion',
                'addressCountry' => 'US'
            ),
            'deliveryTime' => array(
                '@type' => 'ShippingDeliveryTime',
                'handlingTime' => array(
                    '@type' => 'QuantitativeValue',
                    'minValue' => 1,
                    'maxValue' => 2,
                    'unitCode' => 'DAY'
                ),
                'transitTime' => array(
                    '@type' => 'QuantitativeValue',
                    'minValue' => 3,
                    'maxValue' => 7,
                    'unitCode' => 'DAY'
                )
            )
        ),
        // Return policy reference
        'hasMerchantReturnPolicy' => array(
            '@type' => 'MerchantReturnPolicy',
            '@id' => $return_policy_url . '#return-policy',
            'applicableCountry' => 'US',
            'returnPolicyCategory' => 'https://schema.org/MerchantReturnFiniteReturnWindow',
            'merchantReturnDays' => 30,
            'returnMethod' => 'https://schema.org/ReturnByMail',
            'returnFees' => 'https://schema.org/FreeReturn',
            'url' => $return_policy_url
        )
    )
);

// Add category if available
$categories = wp_get_post_terms($post->ID, 'product_cat');
if (!empty($categories) && !is_wp_error($categories)) {
    $schema['category'] = $categories[0]->name;
}

// Add reviews if available
$review_count = $product->get_review_count();
$average_rating = $product->get_average_rating();

if ($review_count > 0 && $average_rating > 0) {
    $schema['aggregateRating'] = array(
        '@type' => 'AggregateRating',
        'ratingValue' => $average_rating,
        'reviewCount' => $review_count,
        'bestRating' => 5,
        'worstRating' => 1
    );
}

// 输出调试信息
$debug_info = array(
    'product_id' => $product_id,
    'product_name' => get_the_title($post->ID),
    'shipping_policy_url' => $shipping_policy_url,
    'return_policy_url' => $return_policy_url,
    'schema_has_shipping' => isset($schema['offers']['shippingDetails']),
    'schema_has_return_policy' => isset($schema['offers']['hasMerchantReturnPolicy']),
    'timestamp' => current_time('mysql'),
    'schema' => $schema
);

echo json_encode($debug_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

wp_reset_postdata();
?>
