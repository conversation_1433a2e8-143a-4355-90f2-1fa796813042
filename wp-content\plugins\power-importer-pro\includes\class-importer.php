<?php
/**
 * 核心导入器类 (V-Final.3 - 最终重构)
 */
if ( ! defined( 'ABSPATH' ) ) { exit; }

class PIP_Importer {
    protected $csv_path;
    protected $variable_product_map = [];
    protected $row_count = 0;
    protected $current_job_id = 0;
    protected $current_processing_post_id = 0;
    
    // 批量操作相关属性
    private $batch_meta_updates = [];
    private $batch_term_updates = [];
    private $batch_size = 50; // 每50条记录批量执行一次
    
    public function __construct($csv_path, $job_id) {
        $this->csv_path = $csv_path;
        $this->current_job_id = (int)$job_id;
    }

    public function run() {
        try {
            pip_db()->update_job($this->current_job_id, ['status' => 'running', 'started_at' => current_time('mysql', 1)]);
            $this->log("--- 开始导入任务 #{$this->current_job_id} ---", 'INFO');

            add_filter( 'upload_dir', [ $this, 'custom_upload_dir' ] );

            if (!file_exists($this->csv_path)) {
                throw new Exception("CSV文件未找到于路径: " . $this->csv_path);
            }

            $file_content = file($this->csv_path, FILE_SKIP_EMPTY_LINES);
            if (false === $file_content) {
                 throw new Exception("无法读取CSV文件: " . $this->csv_path);
            }
            $total_rows = max(0, count($file_content) - 1);
            pip_db()->update_job($this->current_job_id, ['total_rows' => $total_rows]);

            // 🌍 检测和处理文件编码
            $encoding = $this->detect_file_encoding($this->csv_path);
            $this->log("🔍 检测到文件编码: {$encoding}", 'INFO');
            
            // 🚀 性能优化：如果是UTF-8编码，跳过转换步骤
            $need_encoding_conversion = ($encoding !== 'UTF-8' && !empty($encoding));
            if ($need_encoding_conversion) {
                $this->log("⚡ 启用编码转换模式: {$encoding} → UTF-8", 'INFO');
            }

            if (($handle = fopen($this->csv_path, "r")) !== FALSE) {
                $headers = array_map('trim', fgetcsv($handle));
                
                // 🌍 转换头部编码（仅在需要时）
                if ($need_encoding_conversion) {
                    $headers = $this->convert_encoding_array($headers, $encoding);
                }
                
                while (($data = fgetcsv($handle)) !== FALSE) {
                    $this->row_count++;
                    
                    // 🌍 转换数据编码（仅在需要时）
                    if ($need_encoding_conversion) {
                        $data = $this->convert_encoding_array($data, $encoding);
                    }
                    
                    if (count($data) !== count($headers)) {
                        $this->log("⚠️ [行号: {$this->row_count}] 列数与表头不匹配，已跳过。", 'WARNING');
                        continue;
                    }
                    $product_data = array_combine($headers, $data);
                    $this->process_row($product_data);
                    if ($this->row_count % 20 === 0) {
                        pip_db()->update_job($this->current_job_id, ['processed_rows' => $this->row_count]);
                    }
                }
                fclose($handle);
            }
            
            pip_db()->update_job($this->current_job_id, ['processed_rows' => $this->row_count]);
            $this->log("--- 导入数据处理完毕，开始收尾工作 ---", 'SUCCESS');
            
            // 🏁 智能收尾工作控制
            $enable_cleanup = get_option('pip_enable_post_import_cleanup', true);
            if ($enable_cleanup) {
                // 🚀 性能优化：小文件使用轻量收尾，大文件使用完整收尾
                if ($this->row_count <= 100) {
                    $this->perform_lightweight_cleanup();
                } else {
                    $this->perform_post_import_cleanup();
                }
            } else {
                $this->log("⚠️ 收尾工作已禁用，跳过清理步骤", 'WARNING');
            }
            
            // 刷新所有批量操作
            $this->flush_all_batch_operations();
            
            pip_db()->update_job($this->current_job_id, ['status' => 'completed', 'finished_at' => current_time('mysql', 1)]);

        } catch (Exception $e) {
            $this->log("致命错误导致任务中断: " . $e->getMessage(), 'ERROR');
            pip_db()->update_job($this->current_job_id, ['status' => 'failed', 'finished_at' => current_time('mysql', 1)]);
        }

        remove_filter( 'upload_dir', [ $this, 'custom_upload_dir' ] );
    }
    
    /**
     * 🌍 检测文件编码 - 带缓存优化
     */
    private function detect_file_encoding($file_path) {
        // 🚀 性能优化：使用文件信息作为缓存键
        $file_info = pathinfo($file_path);
        $cache_key = 'pip_encoding_' . md5($file_path . filemtime($file_path));
        
        // 使用缓存管理器
        $cache_manager = PIP_Cache_Manager::get_instance();
        return $cache_manager->smart_cache($cache_key, function() use ($file_path, $file_info) {
        $sample = file_get_contents($file_path, false, null, 0, 8192);
        if ($sample === false) {
            return 'UTF-8';
        }
        
        // 🚀 优化：根据文件名预判编码
        $filename_lower = strtolower($file_info['basename']);
        if (strpos($filename_lower, 'utf8') !== false || strpos($filename_lower, 'utf-8') !== false) {
            $detected = 'UTF-8';
        } else {
            // 检测常见编码
            $encodings = ['UTF-8', 'UTF-16', 'UTF-32', 'ISO-8859-1', 'Windows-1252', 'Windows-1251', 'GBK', 'GB2312'];
            $detected = mb_detect_encoding($sample, $encodings, true);
        }
        
        return $detected ?: 'UTF-8';
        }, 'encoding_detection');
    }
    
    /**
     * 🌍 转换数组中所有字符串的编码 - 性能优化版本
     */
    private function convert_encoding_array($array, $from_encoding) {
        if ($from_encoding === 'UTF-8' || empty($from_encoding)) {
            return $array;
        }
        
        // 🚀 性能优化：直接转换，避免逐个检查
        foreach ($array as &$value) {
            if (is_string($value) && !empty($value)) {
                // 直接转换，mb_convert_encoding 会自动处理已经是UTF-8的字符串
                $converted = @mb_convert_encoding($value, 'UTF-8', $from_encoding);
                if ($converted !== false && $converted !== $value) {
                    $value = $converted;
                }
            }
        }
        unset($value); // 清理引用
        
        return $array;
    }
    
    /**
     * ⚡ 轻量收尾工作（适用于小文件导入）
     */
    private function perform_lightweight_cleanup() {
        $start_time = microtime(true);
        $this->log("⚡ 开始执行轻量收尾工作（小文件优化）...", 'INFO');
        
        // 只执行必要的清理，跳过重型操作
        // 1. 基础缓存清理
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // 2. 清理导入临时数据
        delete_option("pip_chunk_state_{$this->current_job_id}");
        
        // 3. 简单统计
        global $wpdb;
        $total_products = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'product' AND post_status = 'publish'");
        $this->log("  📈 产品总数: {$total_products}", 'INFO');
        
        $cleanup_time = round((microtime(true) - $start_time) * 1000, 2);
        $this->log("✅ 轻量收尾完成，耗时: {$cleanup_time}ms", 'SUCCESS');
    }
    
    /**
     * 🏁 执行导入后的完整收尾工作
     */
    protected function perform_post_import_cleanup() {
        $start_time = microtime(true);
        $this->log("🏁 开始执行导入后收尾工作...", 'INFO');
        
        // 1. 🗂️ 清理和优化WooCommerce缓存
        $this->cleanup_woocommerce_cache();
        
        // 2. 🔄 重建产品索引和计数
        $this->rebuild_product_indexes();
        
        // 3. 📊 更新产品统计
        $this->update_product_statistics();
        
        // 4. 🏷️ 优化分类和标签
        $this->optimize_taxonomies();
        
        // 5. 🖼️ 清理无效图片引用
        $this->cleanup_invalid_images();
        
        // 6. 🧹 清理临时数据
        $this->cleanup_temporary_data();
        
        // 7. 📈 生成导入报告
        $this->generate_import_report();
        
        $cleanup_time = round((microtime(true) - $start_time) * 1000, 2);
        $this->log("✅ 收尾工作完成，耗时: {$cleanup_time}ms", 'SUCCESS');
    }
    
    /**
     * 🗂️ 清理和优化WooCommerce缓存
     */
    private function cleanup_woocommerce_cache() {
        $this->log("🗂️ 清理WooCommerce缓存...", 'INFO');
        
        // 清理WooCommerce对象缓存
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients();
        }
        
        // 清理产品查询缓存
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // 清理WooCommerce相关的transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wc_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_wc_%'");
        
        $this->log("  ✓ WooCommerce缓存已清理", 'INFO');
    }
    
    /**
     * 🔄 重建产品索引和计数
     */
    private function rebuild_product_indexes() {
        $this->log("🔄 重建产品索引...", 'INFO');
        
        // 重建WooCommerce产品查找表
        if (class_exists('WC_Install')) {
            WC_Install::create_tables();
        }
        
        // 更新产品可见性
        if (function_exists('wc_update_product_lookup_tables')) {
            wc_update_product_lookup_tables();
        }
        
        // 重建产品属性查找表
        if (function_exists('wc_update_product_attributes_lookup_table')) {
            wc_update_product_attributes_lookup_table();
        }
        
        $this->log("  ✓ 产品索引已重建", 'INFO');
    }
    
    /**
     * 📊 更新产品统计
     */
    private function update_product_statistics() {
        $this->log("📊 更新产品统计...", 'INFO');
        
        global $wpdb;
        
        // 统计新创建的产品数量
        $total_products = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'product' AND post_status = 'publish'");
        $total_variations = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'product_variation' AND post_status = 'publish'");
        
        // 统计有图片的产品
        $products_with_images = $wpdb->get_var("
            SELECT COUNT(DISTINCT p.ID) 
            FROM {$wpdb->posts} p 
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id 
            WHERE p.post_type = 'product' 
            AND p.post_status = 'publish' 
            AND pm.meta_key = '_thumbnail_id' 
            AND pm.meta_value != ''
        ");
        
        $this->log("  📈 产品统计: {$total_products} 个产品, {$total_variations} 个变体, {$products_with_images} 个含图片", 'INFO');
    }
    
    /**
     * 🏷️ 优化分类和标签
     */
    private function optimize_taxonomies() {
        $this->log("🏷️ 优化分类和标签...", 'INFO');
        
        // 更新分类计数
        $taxonomies = ['product_cat', 'product_tag', 'product_brand'];
        foreach ($taxonomies as $taxonomy) {
            if (taxonomy_exists($taxonomy)) {
                wp_update_term_count_now(get_terms($taxonomy, ['fields' => 'ids', 'hide_empty' => false]), $taxonomy);
            }
        }
        
        // 🚨 暂时禁用空分类清理，避免误删刚创建的分类
        // 分类计数可能在导入过程中暂时不准确
        /*
        $empty_terms = get_terms([
            'taxonomy' => ['product_cat', 'product_tag'],
            'hide_empty' => false,
            'count' => 0,
            'fields' => 'ids'
        ]);
        
        if (!empty($empty_terms) && !is_wp_error($empty_terms)) {
            foreach ($empty_terms as $term_id) {
                wp_delete_term($term_id, 'product_cat');
            }
            $this->log("  🗑️ 已清理 " . count($empty_terms) . " 个空分类", 'INFO');
        }
        */
        $this->log("  ⚠️ 跳过空分类清理（避免误删新创建的分类）", 'INFO');
        
        $this->log("  ✓ 分类和标签已优化", 'INFO');
    }
    
    /**
     * 🖼️ 清理无效图片引用
     */
    private function cleanup_invalid_images() {
        $this->log("🖼️ 检查无效图片引用...", 'INFO');
        
        global $wpdb;
        
        // 查找无效的缩略图引用
        $invalid_thumbnails = $wpdb->get_results("
            SELECT pm.post_id, pm.meta_value 
            FROM {$wpdb->postmeta} pm 
            LEFT JOIN {$wpdb->posts} p ON pm.meta_value = p.ID 
            WHERE pm.meta_key = '_thumbnail_id' 
            AND pm.meta_value != '' 
            AND p.ID IS NULL
        ");
        
        $cleaned_count = 0;
        foreach ($invalid_thumbnails as $invalid) {
            delete_post_meta($invalid->post_id, '_thumbnail_id');
            $cleaned_count++;
        }
        
        if ($cleaned_count > 0) {
            $this->log("  🗑️ 已清理 {$cleaned_count} 个无效图片引用", 'INFO');
        } else {
            $this->log("  ✓ 未发现无效图片引用", 'INFO');
        }
    }
    
    /**
     * 🧹 清理临时数据
     */
    private function cleanup_temporary_data() {
        $this->log("🧹 清理临时数据...", 'INFO');
        
        // 1. 清理导入过程中的临时选项
        delete_option("pip_chunk_state_{$this->current_job_id}");
        
        // 2. 清理过期的transients（超过1小时）
        global $wpdb;
        $deleted_transients = $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_timeout_%' 
            AND option_value < %d
        ", time()));
        
        // 3. 清理孤立的transient数据（修复SQL语法错误）
        $orphaned_transients = $wpdb->get_col("
            SELECT option_name FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_%' 
            AND option_name NOT LIKE '_transient_timeout_%'
            AND CONCAT('_transient_timeout_', SUBSTRING(option_name, 12)) NOT IN (
                SELECT option_name FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_%'
            )
            LIMIT 100
        ");
        
        $orphaned_count = 0;
        if (!empty($orphaned_transients)) {
            foreach ($orphaned_transients as $transient_name) {
                if ($wpdb->delete($wpdb->options, ['option_name' => $transient_name])) {
                    $orphaned_count++;
                }
            }
        }
        
        // 4. 🗂️ 清理过期的导入日志（可选，保留最近30天）
        $auto_cleanup_logs = get_option('pip_auto_cleanup_logs', true);
        if ($auto_cleanup_logs) {
            $this->cleanup_old_import_logs();
        }
        
        // 5. 清理Action Scheduler已完成的任务
        $this->cleanup_action_scheduler_logs();
        
        if ($deleted_transients > 0) {
            $this->log("  🗑️ 已清理 {$deleted_transients} 个过期transients", 'INFO');
        }
        
        if ($orphaned_count > 0) {
            $this->log("  🗑️ 已清理 {$orphaned_count} 个孤立transients", 'INFO');
        }
        
        $this->log("  ✓ 临时数据已清理", 'INFO');
    }
    
    /**
     * 🗂️ 清理过期的导入日志（30天前）
     */
    private function cleanup_old_import_logs() {
        global $wpdb;
        
        $logs_table = $wpdb->prefix . 'pip_import_logs';
        $jobs_table = $wpdb->prefix . 'pip_import_jobs';
        
        // 删除30天前已完成任务的日志
        $deleted_logs = $wpdb->query("
            DELETE l FROM {$logs_table} l
            INNER JOIN {$jobs_table} j ON l.job_id = j.id
            WHERE j.status IN ('completed', 'failed', 'cancelled')
            AND j.finished_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        
        // 删除30天前已完成的任务记录
        $deleted_jobs = $wpdb->query("
            DELETE FROM {$jobs_table}
            WHERE status IN ('completed', 'failed', 'cancelled')
            AND finished_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        
        if ($deleted_logs > 0 || $deleted_jobs > 0) {
            $this->log("  🗑️ 已清理30天前的记录: {$deleted_jobs} 个任务, {$deleted_logs} 条日志", 'INFO');
        }
    }
    
    /**
     * 🧹 清理Action Scheduler日志
     */
    private function cleanup_action_scheduler_logs() {
        if (!function_exists('as_get_scheduled_actions')) {
            return;
        }
        
        global $wpdb;
        
        // 清理已完成的Action Scheduler日志（7天前）
        $as_actions_table = $wpdb->prefix . 'actionscheduler_actions';
        $as_logs_table = $wpdb->prefix . 'actionscheduler_logs';
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$as_actions_table}'")) {
            $deleted_actions = $wpdb->query("
                DELETE FROM {$as_actions_table}
                WHERE status IN ('complete', 'failed', 'canceled')
                AND scheduled_date_gmt < DATE_SUB(UTC_TIMESTAMP(), INTERVAL 7 DAY)
                AND hook LIKE 'pip_%'
            ");
            
            if ($deleted_actions > 0) {
                $this->log("  🗑️ 已清理 {$deleted_actions} 个Action Scheduler记录", 'INFO');
            }
        }
    }
    
    /**
     * 📈 生成导入报告
     */
    private function generate_import_report() {
        $this->log("📈 生成导入报告...", 'INFO');
        
        global $wpdb;
        
        // 获取导入统计
        $job = pip_db()->get_job($this->current_job_id);
        $logs = pip_db()->get_logs_for_job($this->current_job_id);
        
        // 统计各类日志
        $log_stats = [
            'SUCCESS' => 0,
            'ERROR' => 0,
            'WARNING' => 0,
            'INFO' => 0
        ];
        
        foreach ($logs as $log) {
            if (isset($log_stats[$log->log_level])) {
                $log_stats[$log->log_level]++;
            }
        }
        
        // 计算处理时间
        $start_time = $job->started_at ? strtotime($job->started_at) : 0;
        $end_time = $job->finished_at ? strtotime($job->finished_at) : time();
        $total_time = max(0, $end_time - $start_time);
        $time_formatted = gmdate("H:i:s", $total_time);
        
        // 生成报告
        $this->log("", 'INFO');
        $this->log("📊 ===== 导入完成报告 =====", 'INFO');
        $this->log("📄 文件名: " . basename($this->csv_path), 'INFO');
        $this->log("⏱️ 总耗时: {$time_formatted}", 'INFO');
        $this->log("📝 处理行数: {$this->row_count}", 'INFO');
        $this->log("✅ 成功: {$log_stats['SUCCESS']} 条", 'INFO');
        $this->log("⚠️ 警告: {$log_stats['WARNING']} 条", 'INFO');
        $this->log("❌ 错误: {$log_stats['ERROR']} 条", 'INFO');
        
        if ($this->row_count > 0 && $total_time > 0) {
            $rate = round($this->row_count / $total_time, 2);
            $this->log("⚡ 处理速度: {$rate} 行/秒", 'INFO');
        }
        
        $this->log("🎉 导入任务圆满完成！", 'SUCCESS');
        $this->log("", 'INFO');
    }
    
    private function log($message, $level = 'INFO') {
        pip_db()->add_log($this->current_job_id, $message, $level);
    }

    /**
     * 预验证主图片是否可访问（可选功能）
     * @param string $images_str 图片URLs字符串
     * @return bool 主图是否可访问
     */
    private function validate_main_image($images_str) {
        if (empty($images_str)) {
            return true; // 允许无图产品
        }
        
        $image_urls = array_filter(array_map('trim', explode(',', $images_str)));
        $main_image_url = $image_urls[0] ?? '';
        
        if (empty($main_image_url)) {
            return true;
        }
        
        // 🚀 性能优化：缓存验证结果，避免重复检查相同URL
        static $url_cache = [];
        $url_hash = md5($main_image_url);
        
        if (isset($url_cache[$url_hash])) {
            return $url_cache[$url_hash];
        }
        
        // 🚀 快速预检：简单的URL格式验证
        if (!filter_var($main_image_url, FILTER_VALIDATE_URL) || 
            !preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', parse_url($main_image_url, PHP_URL_PATH))) {
            $this->log("🖼️ 主图URL格式无效: {$main_image_url}", 'WARNING');
            $url_cache[$url_hash] = false;
            return false;
        }
        
        // 🚀 超快速HEAD请求检查图片可访问性（减少超时时间）
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $main_image_url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5); // 🚀 总超时时间：5秒（HEAD请求足够）
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3); // 🚀 连接超时3秒（适合大图片）
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Power Importer Pro)');
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3); // 🚀 限制重定向次数
        
        $start_time = microtime(true);
        curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $check_time = round((microtime(true) - $start_time) * 1000, 2);
        curl_close($ch);
        
        $is_valid = ($http_code >= 200 && $http_code < 400);
        $url_cache[$url_hash] = $is_valid; // 🚀 缓存结果
        
        if (!$is_valid) {
            $this->log("🖼️ 主图验证失败: {$main_image_url} (HTTP: {$http_code}, {$check_time}ms)", 'WARNING');
        } else {
            $this->log("✅ 主图验证通过: {$main_image_url} ({$check_time}ms)", 'INFO');
        }
        
        return $is_valid;
    }
    
    /**
     * 检查是否应该因为图片问题跳过产品
     * @param array $data 产品数据
     * @param string $name 产品名称
     * @return bool 是否应该跳过
     */
    private function should_skip_product_by_image($data, $name) {
        $strict_mode = get_option('pip_strict_image_mode', false);
        if (!$strict_mode) {
            return false; // 非严格模式，不跳过
        }
        
        $images_str = $data['Images'] ?? '';
        
        // 严格模式下验证主图
        if (!$this->validate_main_image($images_str)) {
            $this->log("⚠️ [行号: {$this->row_count}] 严格模式：主图不可访问，跳过产品 '{$name}'", 'WARNING');
            return true;
        }
        
        return false;
    }

    public function custom_upload_dir( $param ) {
        $base_dir = '/images'; $category_slug = 'uncategorized';
        $post_id = $this->current_processing_post_id;
        if ( $post_id > 0 ) {
            $terms = get_the_terms( $post_id, 'product_cat' );
            if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
                $deepest_term = null;
                foreach ( $terms as $term ) { $children = get_term_children( $term->term_id, 'product_cat' ); if ( empty( $children ) ) { $deepest_term = $term; break; } }
                if ( ! $deepest_term ) { $deepest_term = reset($terms); }
                if ($deepest_term) { $category_slug = $deepest_term->slug; }
            }
        }
        $subdir = $base_dir . '/' . $category_slug;
        $param['path']   = $param['basedir'] . $subdir; $param['url']    = $param['baseurl'] . $subdir; $param['subdir'] = $subdir;
        if ( ! file_exists( $param['path'] ) ) {
            if ( ! wp_mkdir_p( $param['path'] ) ) {
                $this->log( "⚠️ 无法创建专属图片目录 {$param['path']}，将使用默认目录。", 'WARNING' );
                remove_filter('upload_dir', [$this, 'custom_upload_dir']);
                return $GLOBALS['wp_upload_dir'];
            }
        }
        return $param;
    }
    
    private function get_product_id_by_sku_direct($sku) {
        global $wpdb;
        $product_id = $wpdb->get_var($wpdb->prepare("SELECT post_id FROM $wpdb->postmeta WHERE meta_key='_sku' AND meta_value='%s' LIMIT 1", $sku));
        return $product_id ? (int)$product_id : 0;
    }

    protected function process_row($product_data) {
        $type = strtolower(trim($product_data['Type'] ?? 'simple'));
        $name = trim($product_data['Name'] ?? 'Untitled Product');
        switch ($type) {
            case 'simple': case 'variable': $this->process_parent_product($product_data, $type, $name); break;
            case 'variation': $this->process_variation_product($product_data, $name); break;
            default: $this->log("❓ [行号: {$this->row_count}] 未知的产品类型 '{$type}'，已跳过。", 'WARNING'); break;
        }
    }
    
    private function process_parent_product($data, $type, $name) {
        $sku = trim($data['SKU']);
        if (empty($sku)) { $this->log("❌ [行号: {$this->row_count}] {$type} 类型产品 SKU 为空，无法创建 '{$name}'。", 'ERROR'); return; }
        if ($existing_id = $this->get_product_id_by_sku_direct($sku)) { $this->log("↪️ [行号: {$this->row_count}] SKU '{$sku}' 已存在 (ID: {$existing_id})，跳过产品 '{$name}'。", 'INFO'); $this->variable_product_map[$sku] = $existing_id; return; }
        
        // 可选的图片预验证
        if ($this->should_skip_product_by_image($data, $name)) {
            return; // 跳过图片不可访问的产品
        }
        kses_remove_filters();
        $post_id = wp_insert_post(['post_title' => $name, 'post_content' => $data['Description'] ?? '', 'post_excerpt' => $data['Short description'] ?? '', 'post_status'  => 'publish', 'post_type' => 'product']);
        kses_init_filters();
        if (is_wp_error($post_id) || !$post_id) { $error_message = is_wp_error($post_id) ? $post_id->get_error_message() : '返回了无效的产品ID(0)。'; $this->log("❌ [行号: {$this->row_count}] 创建产品 '{$name}' 失败: " . $error_message, 'ERROR'); return; }
        
        $this->variable_product_map[$sku] = $post_id;
        wp_set_object_terms($post_id, $type, 'product_type');
        update_post_meta($post_id, '_sku', $sku);
        $this->set_categories($post_id, $data['Categories'] ?? '');
        
        $this->current_processing_post_id = $post_id;

        // 🏷️ 价格设置
        $regular_price = $data['Regular price'] ?? ''; 
        $sale_price = $data['Sale price'] ?? '';
        update_post_meta($post_id, '_regular_price', $regular_price);
        if (!empty($sale_price)) { 
            update_post_meta($post_id, '_sale_price', $sale_price); 
            update_post_meta($post_id, '_price', $sale_price); 
        } else { 
            update_post_meta($post_id, '_price', $regular_price); 
        }
        
        // 🗓️ 促销日期设置
        if (!empty($data['Date sale price starts'])) {
            update_post_meta($post_id, '_sale_price_dates_from', strtotime($data['Date sale price starts']));
        }
        if (!empty($data['Date sale price ends'])) {
            update_post_meta($post_id, '_sale_price_dates_to', strtotime($data['Date sale price ends']));
        }
        
        // 📦 库存设置
        $manage_stock = ($data['Manage stock'] ?? '0') === '1' ? 'yes' : 'no';
        update_post_meta($post_id, '_manage_stock', $manage_stock);
        
        if ($manage_stock === 'yes') {
            $stock_quantity = intval($data['Stock'] ?? 0);
            update_post_meta($post_id, '_stock', $stock_quantity);
            $stock_status = $stock_quantity > 0 ? 'instock' : 'outofstock';
        } else {
            $stock_status = ($data['In stock'] ?? '1') === '1' ? 'instock' : 'outofstock';
        }
        update_post_meta($post_id, '_stock_status', $stock_status);
        
        // 📏 尺寸重量设置
        if (!empty($data['Weight'])) {
            update_post_meta($post_id, '_weight', $data['Weight']);
        }
        if (!empty($data['Length'])) {
            update_post_meta($post_id, '_length', $data['Length']);
        }
        if (!empty($data['Width'])) {
            update_post_meta($post_id, '_width', $data['Width']);
        }
        if (!empty($data['Height'])) {
            update_post_meta($post_id, '_height', $data['Height']);
        }
        
        // 📋 其他WooCommerce字段
        if (!empty($data['Featured'])) {
            update_post_meta($post_id, '_featured', ($data['Featured'] === '1') ? 'yes' : 'no');
        }
        if (!empty($data['Catalog visibility'])) {
            update_post_meta($post_id, '_visibility', $data['Catalog visibility']);
        }
        if (!empty($data['Tax status'])) {
            update_post_meta($post_id, '_tax_status', $data['Tax status']);
        }
        if (!empty($data['Tax class'])) {
            update_post_meta($post_id, '_tax_class', $data['Tax class']);
        }
        if (!empty($data['Tags'])) { $tags = array_map('trim', explode(',', $data['Tags'])); wp_set_object_terms($post_id, $tags, 'product_tag'); }
        $this->set_attributes($post_id, $data, $type === 'variable');
        if (!empty($data['brands']) && taxonomy_exists('product_brand')) { wp_set_object_terms($post_id, trim($data['brands']), 'product_brand', true); }
        if (!empty($data['Meta: rank_math_focus_keyword'])) update_post_meta($post_id, 'rank_math_focus_keyword', $data['Meta: rank_math_focus_keyword']);
        if (!empty($data['Meta: rank_math_title'])) update_post_meta($post_id, 'rank_math_title', $data['Meta: rank_math_title']);
        if (!empty($data['Meta: rank_math_description'])) update_post_meta($post_id, 'rank_math_description', $data['Meta: rank_math_description']);
        // 新增Yoast SEO字段支持
        if (!empty($data['Meta: yoast_title'])) update_post_meta($post_id, '_yoast_wpseo_title', $data['Meta: yoast_title']);
        if (!empty($data['Meta: yoast_metadesc'])) update_post_meta($post_id, '_yoast_wpseo_metadesc', $data['Meta: yoast_metadesc']);
        if (!empty($data['Meta: yoast_focuskw'])) update_post_meta($post_id, '_yoast_wpseo_focuskw', $data['Meta: yoast_focuskw']);
        if (!empty($data['Meta: yoast_canonical'])) update_post_meta($post_id, '_yoast_wpseo_canonical', $data['Meta: yoast_canonical']);
        if (!empty($data['Meta: yoast_robots'])) update_post_meta($post_id, '_yoast_wpseo_meta-robots-noindex', $data['Meta: yoast_robots']);
        
        // 高级产品字段支持
        $this->set_advanced_product_fields($post_id, $data);
        
        $this->set_images($post_id, $data['Images'] ?? '', $name);
        $this->current_processing_post_id = 0;
        
        $this->log("✅ [行号: {$this->row_count}] 成功创建 {$type} 产品: '{$name}' (ID: {$post_id})", 'SUCCESS');
    }

    private function process_variation_product($data, $name) {
        $sku = trim($data['SKU']);
        if (empty($sku) || $this->get_product_id_by_sku_direct($sku)) { $log_message = empty($sku) ? "变体SKU为空" : "变体SKU '{$sku}' 已存在"; $this->log("  ↳ [行号: {$this->row_count}] {$log_message}，跳过创建。", 'INFO'); return; }
        
        $parent_sku = trim($data['Parent']); $parent_id = null;
        if (isset($this->variable_product_map[$parent_sku])) { $parent_id = $this->variable_product_map[$parent_sku]; } else { $parent_id = $this->get_product_id_by_sku_direct($parent_sku); }
        if (!$parent_id) { $this->log("❌ [行号: {$this->row_count}] 变体 '{$name}' 找不到父产品 (SKU: {$parent_sku})，已跳过。", 'ERROR'); return; }
        
        $this->current_processing_post_id = $parent_id;
        $this->variable_product_map[$parent_sku] = $parent_id;

        $variation = new WC_Product_Variation(); $variation->set_parent_id($parent_id);
        $attributes = [];
        if (!empty($data['Attribute 1 name']) && !empty($data['Attribute 1 value(s)'])) { $attr_name = wc_sanitize_taxonomy_name(trim($data['Attribute 1 name'])); $attr_value = trim($data['Attribute 1 value(s)']); $attributes['attribute_' . $attr_name] = $attr_value; }
        $variation->set_attributes($attributes);
        $variation->set_sku($sku); 
        $variation->set_regular_price($data['Regular price'] ?? '');
        if (!empty($data['Sale price'])) { 
            $variation->set_sale_price($data['Sale price'] ?? ''); 
        }
        
        // 📦 变体库存设置
        $manage_stock = ($data['Manage stock'] ?? '0') === '1';
        $variation->set_manage_stock($manage_stock);
        
        if ($manage_stock) {
            $stock_quantity = intval($data['Stock'] ?? 0);
            $variation->set_stock_quantity($stock_quantity);
            $stock_status = $stock_quantity > 0 ? 'instock' : 'outofstock';
        } else {
            $stock_status = ($data['In stock'] ?? '1') === '1' ? 'instock' : 'outofstock';
        }
        $variation->set_stock_status($stock_status);
        
        // 📏 变体尺寸重量
        if (!empty($data['Weight'])) {
            $variation->set_weight($data['Weight']);
        }
        if (!empty($data['Length'])) {
            $variation->set_length($data['Length']);
        }
        if (!empty($data['Width'])) {
            $variation->set_width($data['Width']);
        }
        if (!empty($data['Height'])) {
            $variation->set_height($data['Height']);
        }
        
        $variation_id = $variation->save();
        
        $parent_product_title = get_the_title($parent_id); $variation_image_alt = $parent_product_title . ' - ' . ($data['Attribute 1 value(s)'] ?? $sku);
        $this->set_images($variation_id, $data['Images'] ?? '', $variation_image_alt, true);
        $this->current_processing_post_id = 0;
        
        $this->log("  ↳ [行号: {$this->row_count}] 成功创建变体 (ID: {$variation_id}) 并关联到父产品 '{$parent_sku}'", 'SUCCESS');
    }

    private function set_images($post_id, $images_str, $post_title, $is_variation = false) {
        if (empty($images_str)) return;
        
        // 🏷️ 激活分类目录过滤器
        add_filter('upload_dir', [$this, 'custom_upload_dir']);
        
        $image_urls = array_filter(array_map('trim', explode(',', $images_str)));
        $gallery_ids = []; $thumbnail_set = false;
        
        foreach ($image_urls as $index => $url) {
            $filename = sanitize_title($post_title) . '-' . $post_id . '-' . ($index + 1);
            $attachment_id = pip_wp_save_img($url, $filename, $post_title);
            if ($attachment_id && !is_wp_error($attachment_id)) {
                if (!$thumbnail_set) { 
                    if($is_variation) { 
                        update_post_meta($post_id, '_thumbnail_id', $attachment_id); 
                    } else { 
                        set_post_thumbnail($post_id, $attachment_id); 
                    } 
                    $thumbnail_set = true; 
                } else { 
                    if (!$is_variation) { 
                        $gallery_ids[] = $attachment_id; 
                    } 
                }
            } else { 
                $this->log("  - ⚠️ 图片下载或处理失败: {$url}", 'WARNING'); 
            }
        }
        
        // 🏷️ 移除分类目录过滤器
        remove_filter('upload_dir', [$this, 'custom_upload_dir']);
        
        if (!$is_variation && !empty($gallery_ids)) { 
            update_post_meta($post_id, '_product_image_gallery', implode(',', $gallery_ids)); 
        }
    }

    private function set_categories($post_id, $categories_str) {
        if (empty($categories_str)) {
            return;
        }
        
        $this->log("  - 🏷️ 开始设置分类: {$categories_str}", 'INFO');
        $category_paths = array_map('trim', explode(',', $categories_str)); 
        $term_ids = [];
        
        foreach ($category_paths as $path) {
            $parts = array_map('trim', explode('>', $path)); 
            $parent_id = 0; 
            $term_id = 0;
            
            foreach ($parts as $part) {
                if (empty($part)) continue;
                
                $term = term_exists($part, 'product_cat', $parent_id);
                if (!$term) { 
                    $term = wp_insert_term($part, 'product_cat', ['parent' => $parent_id]); 
                }
                $term_id = is_array($term) ? $term['term_id'] : $term;
                $parent_id = $term_id;
            }
            
            if ($term_id) {
                $term_ids[] = intval($term_id);
            }
        }
        
        if (!empty($term_ids)) { 
            wp_set_object_terms($post_id, array_unique($term_ids), 'product_cat');
            $this->log("  - ✅ 成功设置产品分类: " . implode(', ', array_unique($term_ids)), 'SUCCESS');
        }
    }
    
    private function set_attributes($post_id, $data, $is_for_variation) {
        $attributes = [];
        for ($i = 1; $i <= 3; $i++) {
            $name_key = "Attribute $i name"; $value_key = "Attribute $i value(s)"; $global_key = "Attribute $i global"; $visible_key = "Attribute $i visible";
            if (!empty($data[$name_key]) && !empty($data[$value_key])) {
                $attr_name = trim($data[$name_key]); $is_global = ($data[$global_key] ?? '0') === '1';
                $taxonomy_name = $is_global ? wc_attribute_taxonomy_name($attr_name) : wc_sanitize_taxonomy_name($attr_name);
                $values = array_map('trim', explode(',', $data[$value_key]));
                if($is_global){
                    if (!taxonomy_exists($taxonomy_name)) { wc_create_attribute(['name' => $attr_name, 'slug' => wc_sanitize_taxonomy_name($attr_name)]); register_taxonomy($taxonomy_name, ['product', 'product_variation']); }
                    foreach($values as $value){ if(!term_exists($value, $taxonomy_name)){ wp_insert_term($value, $taxonomy_name); } }
                    wp_set_object_terms($post_id, $values, $taxonomy_name);
                }
                $attributes[$taxonomy_name] = [ 'name' => $is_global ? $taxonomy_name : $attr_name, 'value' => implode(' | ', $values), 'position' => $i - 1, 'is_visible' => ($data[$visible_key] ?? '0') === '1' ? 1 : 0, 'is_variation' => $is_for_variation, 'is_taxonomy' => $is_global ? 1 : 0, ];
            }
        }
        if (!empty($attributes)) { update_post_meta($post_id, '_product_attributes', $attributes); }
    }
    
    /**
     * 批量数据库操作方法
     */
    
    /**
     * 添加到批量meta更新队列
     */
    private function add_to_batch_meta($post_id, $meta_key, $meta_value) {
        $this->batch_meta_updates[] = [
            'post_id' => $post_id,
            'meta_key' => $meta_key,
            'meta_value' => $meta_value
        ];
        
        // 达到批量大小时执行
        if (count($this->batch_meta_updates) >= $this->batch_size) {
            $this->flush_batch_meta_updates();
        }
    }
    
    /**
     * 执行批量meta更新
     */
    private function flush_batch_meta_updates() {
        if (empty($this->batch_meta_updates)) return;
        
        global $wpdb;
        $values = [];
        $placeholders = [];
        
        foreach ($this->batch_meta_updates as $update) {
            $values[] = $update['post_id'];
            $values[] = $update['meta_key'];
            $values[] = $update['meta_value'];
            $placeholders[] = "(%d, %s, %s)";
        }
        
        $query = "INSERT INTO {$wpdb->postmeta} (post_id, meta_key, meta_value) VALUES " . 
                 implode(', ', $placeholders) . 
                 " ON DUPLICATE KEY UPDATE meta_value = VALUES(meta_value)";
        
        $wpdb->query($wpdb->prepare($query, $values));
        $this->batch_meta_updates = [];
    }
    
    /**
     * 添加到批量term更新队列
     */
    private function add_to_batch_terms($post_id, $terms, $taxonomy) {
        $this->batch_term_updates[] = [
            'post_id' => $post_id,
            'terms' => $terms,
            'taxonomy' => $taxonomy
        ];
        
        // 达到批量大小时执行
        if (count($this->batch_term_updates) >= $this->batch_size) {
            $this->flush_batch_term_updates();
        }
    }
    
    /**
     * 执行批量term更新
     */
    private function flush_batch_term_updates() {
        if (empty($this->batch_term_updates)) return;
        
        foreach ($this->batch_term_updates as $update) {
            wp_set_object_terms($update['post_id'], $update['terms'], $update['taxonomy']);
        }
        
        $this->batch_term_updates = [];
    }
    
    /**
     * 刷新所有批量操作（在导入结束时调用）
     */
    private function flush_all_batch_operations() {
        $this->flush_batch_meta_updates();
        $this->flush_batch_term_updates();
    }
    
    /**
     * 设置高级产品字段
     */
    private function set_advanced_product_fields($post_id, $data) {
        // 交叉销售和向上销售
        if (!empty($data['Cross-sells'])) {
            $cross_sell_ids = $this->get_product_ids_by_skus($data['Cross-sells']);
            if (!empty($cross_sell_ids)) {
                update_post_meta($post_id, '_crosssell_ids', $cross_sell_ids);
            }
        }
        
        if (!empty($data['Upsells'])) {
            $upsell_ids = $this->get_product_ids_by_skus($data['Upsells']);
            if (!empty($upsell_ids)) {
                update_post_meta($post_id, '_upsell_ids', $upsell_ids);
            }
        }
        
        // 分组产品
        if (!empty($data['Grouped products'])) {
            $grouped_ids = $this->get_product_ids_by_skus($data['Grouped products']);
            if (!empty($grouped_ids)) {
                update_post_meta($post_id, '_children', $grouped_ids);
            }
        }
        
        // 外部产品
        if (!empty($data['External product URL'])) {
            update_post_meta($post_id, '_product_url', $data['External product URL']);
            update_post_meta($post_id, '_button_text', $data['Button text'] ?? 'Buy product');
        }
        
        // 虚拟和可下载产品
        if (!empty($data['Virtual'])) {
            update_post_meta($post_id, '_virtual', ($data['Virtual'] === '1') ? 'yes' : 'no');
        }
        
        if (!empty($data['Downloadable'])) {
            update_post_meta($post_id, '_downloadable', ($data['Downloadable'] === '1') ? 'yes' : 'no');
            
            // 下载文件
            if (!empty($data['Download files'])) {
                $download_files = $this->parse_download_files($data['Download files']);
                if (!empty($download_files)) {
                    update_post_meta($post_id, '_downloadable_files', $download_files);
                }
            }
            
            // 下载限制
            if (!empty($data['Download limit'])) {
                update_post_meta($post_id, '_download_limit', intval($data['Download limit']));
            }
            
            // 下载过期
            if (!empty($data['Download expiry'])) {
                update_post_meta($post_id, '_download_expiry', intval($data['Download expiry']));
            }
        }
        
        // 高级库存管理
        if (!empty($data['Low stock amount'])) {
            update_post_meta($post_id, '_low_stock_amount', intval($data['Low stock amount']));
        }
        
        if (!empty($data['Backorders'])) {
            $backorder_status = $this->map_backorder_status($data['Backorders']);
            update_post_meta($post_id, '_backorders', $backorder_status);
        }
        
        // 评论设置
        if (!empty($data['Enable reviews'])) {
            $comment_status = ($data['Enable reviews'] === '1') ? 'open' : 'closed';
            wp_update_post(['ID' => $post_id, 'comment_status' => $comment_status]);
        }
        
        // 购买说明
        if (!empty($data['Purchase note'])) {
            update_post_meta($post_id, '_purchase_note', $data['Purchase note']);
        }
        
        // 运输类别
        if (!empty($data['Shipping class'])) {
            $shipping_class = sanitize_title($data['Shipping class']);
            wp_set_object_terms($post_id, $shipping_class, 'product_shipping_class');
        }
    }
    
    /**
     * 根据SKU列表获取产品ID列表
     */
    private function get_product_ids_by_skus($skus_string) {
        if (empty($skus_string)) return [];
        
        $skus = array_map('trim', explode(',', $skus_string));
        $product_ids = [];
        
        foreach ($skus as $sku) {
            $product_id = $this->get_product_id_by_sku_direct($sku);
            if ($product_id) {
                $product_ids[] = $product_id;
            }
        }
        
        return $product_ids;
    }
    
    /**
     * 解析下载文件字符串
     */
    private function parse_download_files($files_string) {
        $files = [];
        $file_entries = explode('|', $files_string);
        
        foreach ($file_entries as $entry) {
            $parts = explode(':', $entry);
            if (count($parts) >= 2) {
                $name = trim($parts[0]);
                $url = trim($parts[1]);
                $files[$name] = ['name' => $name, 'file' => $url];
            }
        }
        
        return $files;
    }
    
    /**
     * 映射缺货预订状态
     */
    private function map_backorder_status($backorder_value) {
        switch (strtolower($backorder_value)) {
            case 'yes':
            case '1':
                return 'yes';
            case 'notify':
            case 'notify_customer':
                return 'notify';
            case 'no':
            case '0':
            default:
                return 'no';
        }
    }
}