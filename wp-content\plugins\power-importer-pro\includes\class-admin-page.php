<?php
/**
 * 插件的后台管理页面 (V-Final.3 - UI Only)
 */
if ( ! defined( 'ABSPATH' ) ) { exit; }

class PIP_Admin_Page {

    public function __construct() {
        // 构造函数现在只负责注册菜单
        add_action( 'admin_menu', [ $this, 'add_admin_menu' ] );
        // 文件上传处理现在由主插件文件中的全局函数处理
    }
    
    public function add_admin_menu() {
        add_submenu_page( 
            'edit.php?post_type=product', 
            __( 'Power Importer', 'power-importer-pro' ), 
            __( 'Power Importer', 'power-importer-pro' ), 
            'manage_woocommerce', 
            'power-importer-pro', 
            [ $this, 'render_admin_page' ] 
        );
    }

    public function render_admin_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            
            <?php
            if ( isset( $_GET['view_log'] ) && ! empty( $_GET['view_log'] ) ) {
                $this->render_log_details_page( (int) $_GET['view_log'] );
            } else {
                // 处理设置保存
                if ( 'POST' === $_SERVER['REQUEST_METHOD'] && isset( $_POST['pip_settings_nonce'] ) ) {
                    $this->handle_settings_submission();
                }
                
                // 处理文件上传表单的提交
                if ( 'POST' === $_SERVER['REQUEST_METHOD'] && isset( $_POST['pip_import_nonce'] ) ) {
                    $this->handle_form_submission();
                }
                
                // 渲染带标签页的主页面
                $this->render_main_page_with_tabs();
            }
            ?>
        </div>
        <?php
    }

    private function handle_form_submission() {
        if ( ! wp_verify_nonce( $_POST['pip_import_nonce'], 'pip_import_action' ) ) { return; }
        if ( ! current_user_can( 'manage_woocommerce' ) ) { wp_die( 'Access Denied' ); }
        if ( isset( $_FILES['import_csv_files'] ) && !empty($_FILES['import_csv_files']['name'][0]) ) {
            $this->process_uploaded_files( $_FILES['import_csv_files'] );
        } else {
            add_action( 'admin_notices', function() { echo '<div class="notice notice-error is-dismissible"><p>' . __( 'No files were selected for upload.', 'power-importer-pro' ) . '</p></div>'; });
        }
    }
    
    private function handle_settings_submission() {
        if ( ! wp_verify_nonce( $_POST['pip_settings_nonce'], 'pip_settings_action' ) ) { return; }
        if ( ! current_user_can( 'manage_woocommerce' ) ) { wp_die( 'Access Denied' ); }
        
        // 保存设置
        $chunk_size = max(5, min(100, intval($_POST['pip_chunk_size'] ?? 20)));
        $max_execution_time = max(30, min(300, intval($_POST['pip_max_execution_time'] ?? 90)));
        $concurrent_jobs = max(1, min(10, intval($_POST['pip_concurrent_jobs'] ?? 3)));
        $large_file_threshold = max(1, min(50, intval($_POST['pip_large_file_threshold'] ?? 5)));
        $enable_smart_delay = isset($_POST['pip_enable_smart_delay']) ? true : false;
        $strict_image_mode = isset($_POST['pip_strict_image_mode']) ? true : false;
        $enable_post_import_cleanup = isset($_POST['pip_enable_post_import_cleanup']) ? true : false;
        $auto_cleanup_logs = isset($_POST['pip_auto_cleanup_logs']) ? true : false;
        $enable_auto_retry = isset($_POST['pip_enable_auto_retry']) ? true : false;
        
        update_option('pip_chunk_size', $chunk_size);
        update_option('pip_max_execution_time', $max_execution_time);
        update_option('pip_concurrent_jobs', $concurrent_jobs);
        update_option('pip_large_file_threshold', $large_file_threshold);
        update_option('pip_enable_smart_delay', $enable_smart_delay);
        update_option('pip_strict_image_mode', $strict_image_mode);
        update_option('pip_enable_post_import_cleanup', $enable_post_import_cleanup);
        update_option('pip_auto_cleanup_logs', $auto_cleanup_logs);
        update_option('pip_enable_auto_retry', $enable_auto_retry);
        
        add_action( 'admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . __( 'Settings saved successfully!', 'power-importer-pro' ) . '</p></div>';
        });
    }

    private function process_uploaded_files( $files ) {
        $upload_dir = wp_upload_dir();
        $pip_dir = $upload_dir['basedir'] . '/' . PIP_UPLOAD_DIR_NAME;
        if ( ! file_exists( $pip_dir ) || ! is_writable( $pip_dir ) ) { add_action( 'admin_notices', function() use ($pip_dir) { echo '<div class="notice notice-error is-dismissible"><p>' . sprintf( __( 'Upload directory is not writable or does not exist: %s', 'power-importer-pro' ), '<code>' . esc_html($pip_dir) . '</code>' ) . '</p></div>'; }); return; }
        $allowed_types = [ 'text/csv', 'text/plain', 'application/vnd.ms-excel' ];
        $scheduled_count = 0; $error_count = 0;
        foreach ( $files['name'] as $key => $name ) {
            if ( $files['error'][$key] !== UPLOAD_ERR_OK ) { $error_count++; continue; }
            if ( ! in_array( $files['type'][$key], $allowed_types ) ) { $error_count++; continue; }
            $filename = 'import-' . time() . '-' . sanitize_file_name( $name );
            $filepath = $pip_dir . '/' . $filename;
            if ( move_uploaded_file( $files['tmp_name'][$key], $filepath ) ) {
                $job_id = pip_db()->create_job(basename($filepath), $filepath);
                if ($job_id) {
                    as_enqueue_async_action( 'pip_process_import_file', [ $job_id ], 'power-importer-pro-group' );
                    $scheduled_count++;
                } else { $error_count++; }
            } else { $error_count++; }
        }
        add_action( 'admin_notices', function() use ($scheduled_count, $error_count) {
            if ($scheduled_count > 0) { echo '<div class="notice notice-success is-dismissible"><p>' . sprintf( _n( '%d import job has been scheduled.', '%d import jobs have been scheduled.', $scheduled_count, 'power-importer-pro' ), $scheduled_count ) . '</p></div>'; }
            if ($error_count > 0) { echo '<div class="notice notice-error is-dismissible"><p>' . sprintf( _n( '%d file failed to upload or was skipped.', '%d files failed to upload or were skipped.', $error_count, 'power-importer-pro' ), $error_count ) . '</p></div>'; }
        });
    }

    private function render_main_page_with_tabs() {
        $current_tab = $_GET['tab'] ?? 'import';
        ?>
        <h2 class="nav-tab-wrapper">
            <a href="<?php echo admin_url('edit.php?post_type=product&page=power-importer-pro&tab=import'); ?>" 
               class="nav-tab <?php echo $current_tab === 'import' ? 'nav-tab-active' : ''; ?>">
                📁 <?php _e('Import Files', 'power-importer-pro'); ?>
            </a>
            <a href="<?php echo admin_url('edit.php?post_type=product&page=power-importer-pro&tab=settings'); ?>" 
               class="nav-tab <?php echo $current_tab === 'settings' ? 'nav-tab-active' : ''; ?>">
                ⚙️ <?php _e('Settings', 'power-importer-pro'); ?>
            </a>
            <a href="<?php echo admin_url('edit.php?post_type=product&page=power-importer-pro&tab=status'); ?>" 
               class="nav-tab <?php echo $current_tab === 'status' ? 'nav-tab-active' : ''; ?>">
                📊 <?php _e('System Status', 'power-importer-pro'); ?>
            </a>
        </h2>
        
        <?php
        switch ($current_tab) {
            case 'settings':
                $this->render_settings_page();
                break;
            case 'status':
                $this->render_status_page();
                break;
            default:
                $this->render_import_page();
                break;
        }
    }
    
    private function render_import_page() {
        ?>
        <div class="card" style="max-width: 600px; margin-top: 20px;">
            <h2 class="title"><?php _e( 'Upload New Import File(s)', 'power-importer-pro' ); ?></h2>
            <p><?php _e( 'Select one or more CSV/TXT files to import. Each file will be processed as a separate job in the background.', 'power-importer-pro' ); ?></p>
            <form method="post" enctype="multipart/form-data">
                <?php wp_nonce_field( 'pip_import_action', 'pip_import_nonce' ); ?>
                <table class="form-table" role="presentation">
                    <tr><th scope="row"><label for="import_csv_files"><?php _e( 'Import Files', 'power-importer-pro' ); ?></label></th>
                    <td><input type="file" id="import_csv_files" name="import_csv_files[]" accept=".csv, .txt" required multiple><p class="description"><?php _e('Maximum upload file size:', 'power-importer-pro'); echo ' ' . size_format(wp_max_upload_size()); ?></p></td></tr>
                </table>
                <?php submit_button( __( 'Upload and Schedule Import', 'power-importer-pro' ), 'primary' ); ?>
            </form>
        </div>
        <div id="import-status-area" style="margin-top: 40px;">
            <h2><?php _e( 'Import Jobs', 'power-importer-pro' ); ?></h2>
            <?php $this->render_jobs_table(); ?>
        </div>
        <?php
    }
    
    private function render_settings_page() {
        // 获取当前设置值
        $chunk_size = get_option('pip_chunk_size', 20);
        $max_execution_time = get_option('pip_max_execution_time', 90);
        $concurrent_jobs = get_option('pip_concurrent_jobs', 1);  // 更新默认值为1
        $large_file_threshold = get_option('pip_large_file_threshold', 3);  // 修改默认为3MB
        $enable_smart_delay = get_option('pip_enable_smart_delay', true);
        $strict_image_mode = get_option('pip_strict_image_mode', false);
        
        // 获取推荐配置
        $recommended = PIP_Config_Helper::get_recommended_config();
        ?>
        <div class="card" style="margin-top: 20px;">
            <h2 class="title">⚙️ <?php _e('Performance Settings', 'power-importer-pro'); ?></h2>
            <p><?php _e('Configure the import processing behavior based on your server performance.', 'power-importer-pro'); ?></p>
            
            <div class="notice notice-info">
                <p><strong>🔧 <?php _e('Server Performance Level:', 'power-importer-pro'); ?></strong> 
                   <span class="pip-performance-level"><?php echo ucfirst($recommended['performance_level']); ?></span>
                   <span style="margin-left: 20px;">
                       <em><?php printf(__('Recommended: %d products per chunk, %d seconds timeout', 'power-importer-pro'), $recommended['chunk_size'], $recommended['max_execution_time']); ?></em>
                   </span>
                </p>
            </div>
            
            <form method="post">
                <?php wp_nonce_field('pip_settings_action', 'pip_settings_nonce'); ?>
                
                <table class="form-table" role="presentation">
                    <tr>
                        <th scope="row"><label for="pip_chunk_size"><?php _e('Chunk Size', 'power-importer-pro'); ?></label></th>
                        <td>
                            <input type="number" id="pip_chunk_size" name="pip_chunk_size" value="<?php echo esc_attr($chunk_size); ?>" min="5" max="100" class="small-text">
                            <p class="description">
                                <?php _e('Number of products to process in each chunk (5-100).', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Recommended:', 'power-importer-pro'); ?></strong> <?php echo $recommended['chunk_size']; ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><label for="pip_max_execution_time"><?php _e('Max Execution Time', 'power-importer-pro'); ?></label></th>
                        <td>
                            <input type="number" id="pip_max_execution_time" name="pip_max_execution_time" value="<?php echo esc_attr($max_execution_time); ?>" min="30" max="300" class="small-text">
                            <span><?php _e('seconds', 'power-importer-pro'); ?></span>
                            <p class="description">
                                <?php _e('Maximum time for each chunk processing (30-300 seconds).', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Recommended:', 'power-importer-pro'); ?></strong> <?php echo $recommended['max_execution_time']; ?> <?php _e('seconds', 'power-importer-pro'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><label for="pip_concurrent_jobs"><?php _e('Concurrent Jobs', 'power-importer-pro'); ?></label></th>
                        <td>
                            <input type="number" id="pip_concurrent_jobs" name="pip_concurrent_jobs" value="<?php echo esc_attr($concurrent_jobs); ?>" min="1" max="10" class="small-text">
                            <p class="description">
                                <?php _e('Number of import jobs to run simultaneously (1-10).', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Recommended:', 'power-importer-pro'); ?></strong> <?php echo $recommended['concurrent_jobs']; ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><label for="pip_large_file_threshold"><?php _e('Large File Threshold', 'power-importer-pro'); ?></label></th>
                        <td>
                            <input type="number" id="pip_large_file_threshold" name="pip_large_file_threshold" value="<?php echo esc_attr($large_file_threshold); ?>" min="1" max="50" class="small-text">
                            <span><?php _e('MB', 'power-importer-pro'); ?></span>
                            <p class="description">
                                <?php _e('Files larger than this size will use chunked processing (1-50 MB).', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Current:', 'power-importer-pro'); ?></strong> <?php _e('Files > ', 'power-importer-pro'); ?><?php echo $large_file_threshold; ?><?php _e('MB use chunked processing', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Recommended:', 'power-importer-pro'); ?></strong> <?php _e('3MB (balance between speed and stability)', 'power-importer-pro'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Smart Delay', 'power-importer-pro'); ?></th>
                        <td>
                            <label for="pip_enable_smart_delay">
                                <input type="checkbox" id="pip_enable_smart_delay" name="pip_enable_smart_delay" value="1" <?php checked($enable_smart_delay); ?>>
                                <?php _e('Enable intelligent delay between chunks', 'power-importer-pro'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Automatically add 1-3 second delays when server load is high.', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Note:', 'power-importer-pro'); ?></strong> <?php _e('Only recommended for shared hosting with strict resource limits.', 'power-importer-pro'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Image Validation', 'power-importer-pro'); ?></th>
                        <td>
                            <label for="pip_strict_image_mode">
                                <input type="checkbox" id="pip_strict_image_mode" name="pip_strict_image_mode" value="1" <?php checked(get_option('pip_strict_image_mode', false)); ?>>
                                <?php _e('Enable strict image validation', 'power-importer-pro'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Skip products when the main image cannot be accessed (fast HTTP HEAD check).', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Method:', 'power-importer-pro'); ?></strong> <?php _e('5-second timeout + URL caching + format validation', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Impact:', 'power-importer-pro'); ?></strong> <?php _e('Adds ~5-10% processing time but prevents invalid products.', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Default:', 'power-importer-pro'); ?></strong> <?php _e('Products are created even if images fail to download.', 'power-importer-pro'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Post-Import Cleanup', 'power-importer-pro'); ?></th>
                        <td>
                            <label for="pip_enable_post_import_cleanup">
                                <input type="checkbox" id="pip_enable_post_import_cleanup" name="pip_enable_post_import_cleanup" value="1" <?php checked(get_option('pip_enable_post_import_cleanup', true)); ?>>
                                <?php _e('Enable automatic post-import cleanup', 'power-importer-pro'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Automatically perform cleanup tasks after import completion.', 'power-importer-pro'); ?>
                                <br><strong><?php _e('Includes:', 'power-importer-pro'); ?></strong> <?php _e('Cache clearing, index rebuilding, statistics update, taxonomy optimization', 'power-importer-pro'); ?>
                            </p>
                            
                            <label for="pip_auto_cleanup_logs" style="margin-top: 10px; display: block;">
                                <input type="checkbox" id="pip_auto_cleanup_logs" name="pip_auto_cleanup_logs" value="1" <?php checked(get_option('pip_auto_cleanup_logs', true)); ?>>
                                <?php _e('Auto-cleanup old logs (30+ days)', 'power-importer-pro'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Automatically remove import logs and job records older than 30 days to save database space.', 'power-importer-pro'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Auto Retry', 'power-importer-pro'); ?></th>
                        <td>
                            <label for="pip_enable_auto_retry">
                                <input type="checkbox" id="pip_enable_auto_retry" name="pip_enable_auto_retry" value="1" <?php checked(get_option('pip_enable_auto_retry', true)); ?>?>
                                <?php _e('Enable automatic retry for failed jobs', 'power-importer-pro'); ?>
                            </label>
                            <p class="description">
                                <?php _e('Automatically retry failed jobs up to 3 times with 5-minute delays. Helps recover from temporary server issues.', 'power-importer-pro'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <div class="submit-section">
                    <?php submit_button(__('Save Settings', 'power-importer-pro'), 'primary'); ?>
                    <button type="button" class="button button-secondary" id="pip-apply-recommended" style="margin-left: 10px;">
                        <?php _e('Apply Recommended Settings', 'power-importer-pro'); ?>
                    </button>
                </div>
            </form>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#pip-apply-recommended').click(function() {
                if (confirm('<?php _e('This will overwrite your current settings. Continue?', 'power-importer-pro'); ?>')) {
                    $('#pip_chunk_size').val(<?php echo $recommended['chunk_size']; ?>);
                    $('#pip_max_execution_time').val(<?php echo $recommended['max_execution_time']; ?>);
                    $('#pip_concurrent_jobs').val(<?php echo $recommended['concurrent_jobs']; ?>);
                    $('#pip_large_file_threshold').val(3);  // 修改推荐设置为3MB
                    $('#pip_enable_smart_delay').prop('checked', false);
                    $('#pip_strict_image_mode').prop('checked', false);
                }
            });
        });
        </script>
        
        <style>
        .pip-performance-level {
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .pip-performance-level:contains('high') { background: #d4edda; color: #155724; }
        .pip-performance-level:contains('medium') { background: #fff3cd; color: #856404; }
        .pip-performance-level:contains('low') { background: #f8d7da; color: #721c24; }
        .submit-section { margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; }
        </style>
        <?php
    }
    
    private function render_status_page() {
        // 获取系统信息
        $memory_limit = ini_get('memory_limit');
        $max_execution_time = ini_get('max_execution_time');
        $max_upload_size = size_format(wp_max_upload_size());
        $php_version = phpversion();
        $wp_version = get_bloginfo('version');
        
        // 检查Action Scheduler状态
        $pending_actions = 0;
        $failed_actions = 0;
        if (function_exists('as_get_scheduled_actions')) {
            $pending_actions = count(as_get_scheduled_actions([
                'group' => 'power-importer-pro-group',
                'status' => 'pending'
            ], 'ids'));
            
            $failed_actions = count(as_get_scheduled_actions([
                'group' => 'power-importer-pro-group', 
                'status' => 'failed'
            ], 'ids'));
        }
        
        // 获取当前设置
        $current_settings = [
            'chunk_size' => get_option('pip_chunk_size', 20),
            'max_execution_time' => get_option('pip_max_execution_time', 90),
            'concurrent_jobs' => get_option('pip_concurrent_jobs', 1),  // 更新默认值为1
            'large_file_threshold' => get_option('pip_large_file_threshold', 3),  // 修改默认为3MB
            'enable_smart_delay' => get_option('pip_enable_smart_delay', true),
            'strict_image_mode' => get_option('pip_strict_image_mode', false)
        ];
        
        ?>
        <div class="card" style="margin-top: 20px;">
            <h2 class="title">📊 <?php _e('System Status', 'power-importer-pro'); ?></h2>
            
            <h3><?php _e('Server Information', 'power-importer-pro'); ?></h3>
            <table class="widefat">
                <tr><td><strong><?php _e('PHP Version:', 'power-importer-pro'); ?></strong></td><td><?php echo $php_version; ?></td></tr>
                <tr><td><strong><?php _e('WordPress Version:', 'power-importer-pro'); ?></strong></td><td><?php echo $wp_version; ?></td></tr>
                <tr><td><strong><?php _e('Memory Limit:', 'power-importer-pro'); ?></strong></td><td><?php echo $memory_limit; ?></td></tr>
                <tr><td><strong><?php _e('Max Execution Time:', 'power-importer-pro'); ?></strong></td><td><?php echo $max_execution_time; ?> <?php _e('seconds', 'power-importer-pro'); ?></td></tr>
                <tr><td><strong><?php _e('Max Upload Size:', 'power-importer-pro'); ?></strong></td><td><?php echo $max_upload_size; ?></td></tr>
                <tr><td><strong><?php _e('Action Scheduler:', 'power-importer-pro'); ?></strong></td><td><?php echo function_exists('as_enqueue_async_action') ? '✅ ' . __('Available', 'power-importer-pro') : '❌ ' . __('Not Available', 'power-importer-pro'); ?></td></tr>
            </table>
            
            <h3 style="margin-top: 30px;"><?php _e('Current Queue Status', 'power-importer-pro'); ?></h3>
            <table class="widefat">
                <tr><td><strong><?php _e('Pending Jobs:', 'power-importer-pro'); ?></strong></td><td><?php echo $pending_actions; ?></td></tr>
                <tr><td><strong><?php _e('Failed Jobs:', 'power-importer-pro'); ?></strong></td><td><?php echo $failed_actions; ?></td></tr>
            </table>
            
            <h3 style="margin-top: 30px;"><?php _e('Current Settings', 'power-importer-pro'); ?></h3>
            <table class="widefat">
                <tr><td><strong><?php _e('Chunk Size:', 'power-importer-pro'); ?></strong></td><td><?php echo $current_settings['chunk_size']; ?> <?php _e('products per chunk', 'power-importer-pro'); ?></td></tr>
                <tr><td><strong><?php _e('Max Execution Time:', 'power-importer-pro'); ?></strong></td><td><?php echo $current_settings['max_execution_time']; ?> <?php _e('seconds', 'power-importer-pro'); ?></td></tr>
                <tr><td><strong><?php _e('Concurrent Jobs:', 'power-importer-pro'); ?></strong></td><td><?php echo $current_settings['concurrent_jobs']; ?></td></tr>
                <tr><td><strong><?php _e('Large File Threshold:', 'power-importer-pro'); ?></strong></td><td><?php echo $current_settings['large_file_threshold']; ?> MB</td></tr>
                <tr><td><strong><?php _e('Smart Delay:', 'power-importer-pro'); ?></strong></td><td><?php echo $current_settings['enable_smart_delay'] ? '✅ ' . __('Enabled', 'power-importer-pro') : '❌ ' . __('Disabled', 'power-importer-pro'); ?></td></tr>
                <tr><td><strong><?php _e('Image Validation:', 'power-importer-pro'); ?></strong></td><td><?php echo $current_settings['strict_image_mode'] ? '✅ ' . __('Strict Mode', 'power-importer-pro') : '❌ ' . __('Lenient Mode', 'power-importer-pro'); ?></td></tr>
            </table>
            
            <h3 style="margin-top: 30px;"><?php _e('Processing Mode Examples', 'power-importer-pro'); ?></h3>
            <table class="widefat">
                <tr><th><?php _e('File Size', 'power-importer-pro'); ?></th><th><?php _e('Processing Mode', 'power-importer-pro'); ?></th><th><?php _e('Estimated Time', 'power-importer-pro'); ?></th></tr>
                <tr>
                    <td>≤3MB (1500-3000 products)</td>
                    <td>⚡ <?php _e('Fast Mode', 'power-importer-pro'); ?></td>
                    <td>3-8 <?php _e('minutes', 'power-importer-pro'); ?></td>
                </tr>
                <tr>
                    <td>10MB (5000-10000 products)</td>
                    <td>🔄 <?php _e('Chunked Mode', 'power-importer-pro'); ?></td>
                    <td>30-60 <?php _e('minutes', 'power-importer-pro'); ?></td>
                </tr>
                <tr>
                    <td>50MB (50000 products)</td>
                    <td>🔄 <?php _e('Chunked Mode', 'power-importer-pro'); ?></td>
                    <td>6-8 <?php _e('hours', 'power-importer-pro'); ?></td>
                </tr>
            </table>
        </div>
        <?php
    }

    private function render_jobs_table() {
        ?>
        <div class="pip-table-actions" style="margin-bottom: 10px; text-align: right;">
             <button class="button button-secondary" id="pip-manual-cleanup-button" data-confirm="<?php esc_attr_e('This will perform system cleanup including cache clearing, index rebuilding, and log cleanup. Continue?', 'power-importer-pro'); ?>"><?php _e('Manual System Cleanup', 'power-importer-pro'); ?></button>
             <button class="button button-secondary" id="pip-clear-jobs-button" data-confirm="<?php esc_attr_e('Are you sure you want to clear all completed, failed, and cancelled jobs? This cannot be undone.', 'power-importer-pro'); ?>"><?php _e('Clear All Finished Jobs', 'power-importer-pro'); ?></button>
        </div>
        <div id="pip-jobs-table-container">
            <?php $this->render_jobs_table_content(); ?>
        </div>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                var pip_interval;
                function refresh_jobs_table() {
                    if ($('#pip-jobs-table-container').length === 0) { if(pip_interval) clearInterval(pip_interval); return; }
                    var needs_refresh = $('.pip-status-running, .pip-status-pending').length > 0;
                    if (needs_refresh || !document.hidden) {
                        $.ajax({ url: ajaxurl, type: 'POST', data: { action: 'pip_get_jobs_table' },
                            success: function(response) { if (response.success) { $('#pip-jobs-table-container').html(response.data.html); } }
                        });
                    }
                }
                pip_interval = setInterval(refresh_jobs_table, 5000);

                $('#pip-jobs-table-container, .pip-table-actions').on('click', 'button', function(e) {
                    e.preventDefault();
                    var $button = $(this);
                    var job_id = $button.data('job-id') || 0;
                    var button_id = $button.attr('id');
                    var job_action = '';
                    
                    // 确定动作类型
                    if (button_id === 'pip-clear-jobs-button') {
                        job_action = 'clear_jobs';
                    } else if (button_id === 'pip-manual-cleanup-button') {
                        job_action = 'manual_cleanup';
                    } else {
                        job_action = $button.data('action');
                    }
                    
                    var confirmation_message = $button.data('confirm');
                    if (confirmation_message && !confirm(confirmation_message)) { return; }
                    var original_text = $button.html(); $button.prop('disabled', true).html('Processing...');
                    
                    // 确定AJAX动作
                    var ajax_action = '';
                    if (job_action === 'clear_jobs') {
                        ajax_action = 'pip_clear_jobs';
                    } else if (job_action === 'manual_cleanup') {
                        ajax_action = 'pip_manual_cleanup';
                    } else {
                        ajax_action = 'pip_job_action';
                    }
                    
                    $.ajax({
                        url: ajaxurl, type: 'POST',
                        data: { action: ajax_action, job_id: job_id, job_action: job_action, nonce: '<?php echo wp_create_nonce("pip_ajax_nonce"); ?>' },
                        success: function(response) { 
                            if (response.data && response.data.message) { 
                                alert(response.data.message); 
                            } 
                            refresh_jobs_table(); 
                            $button.prop('disabled', false).html(original_text);
                        },
                        error: function() { 
                            alert('An unknown error occurred.'); 
                            $button.prop('disabled', false).html(original_text); 
                        }
                    });
                });
            });
        </script>
        <?php
    }

    public function render_jobs_table_content() {
        $jobs = pip_db()->get_recent_jobs();
        ?>
        <style>.pip-status-running { color: blue; font-weight: bold; } .pip-status-completed { color: green; } .pip-status-failed { color: red; font-weight: bold; } .pip-status-pending { color: grey; } .pip-status-cancelled { color: #888; text-decoration: line-through; } .button-link-delete { color: #a00; text-decoration: none; border: none; background: none; cursor: pointer; vertical-align: middle; } .button-link-delete:hover { color: #f00; }</style>
        <table class="wp-list-table widefat fixed striped">
            <thead><tr><th>ID</th><th>File Name</th><th>Status</th><th>Progress</th><th>Started At</th><th>Finished At</th><th>Actions</th></tr></thead>
            <tbody>
                <?php if ( empty( $jobs ) ) : ?>
                    <tr><td colspan="7"><?php _e('No import jobs found.', 'power-importer-pro'); ?></td></tr>
                <?php else : ?>
                    <?php foreach ( $jobs as $job ) : ?>
                        <tr>
                            <td>#<?php echo (int)$job->id; ?></td><td><?php echo esc_html($job->file_name); ?></td>
                            <td>
                                <span class="pip-status-<?php echo esc_attr($job->status); ?>"><?php echo esc_html(ucfirst($job->status)); ?></span>
                                <?php 
                                // 显示重试信息
                                if (class_exists('PIP_Auto_Retry')) {
                                    $retry_info = PIP_Auto_Retry::get_retry_info($job->id);
                                    if ($retry_info && $retry_info['retry_count'] > 0) {
                                        echo '<br><small style="color:#666;">(' . sprintf(__('Retry %d/%d', 'power-importer-pro'), $retry_info['retry_count'], $retry_info['max_retries']) . ')</small>';
                                    }
                                }
                                ?>
                            </td>
                            <td> <?php if ($job->total_rows > 0) { $percentage = round(($job->processed_rows / $job->total_rows) * 100); echo "{$job->processed_rows} / {$job->total_rows} ({$percentage}%)"; echo '<progress value="' . esc_attr($job->processed_rows) . '" max="' . esc_attr($job->total_rows) . '" style="width: 100%; margin-top: 5px;"></progress>'; } else { echo "{$job->processed_rows} / ?"; } ?> </td>
                            <td><?php echo $job->started_at ? date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $job->started_at ) ) : '—'; ?></td>
                            <td><?php echo $job->finished_at ? date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $job->finished_at ) ) : '—'; ?></td>
                            <td class="pip-actions">
                                <?php $log_link = add_query_arg( ['page' => 'power-importer-pro', 'view_log' => $job->id], admin_url('edit.php?post_type=product') ); ?>
                                <a href="<?php echo esc_url( $log_link ); ?>" class="button button-secondary button-small"><?php _e('View Log', 'power-importer-pro'); ?></a>
                                <?php if ( in_array($job->status, ['pending', 'running']) ) : ?> <button class="button button-secondary button-small pip-job-action" data-job-id="<?php echo (int)$job->id; ?>" data-action="cancel" data-confirm="<?php esc_attr_e('Are you sure you want to cancel this job?', 'power-importer-pro'); ?>"><?php _e('Cancel', 'power-importer-pro'); ?></button> <?php endif; ?>
                                <?php if ( $job->status === 'failed' ) : ?> <button class="button button-primary button-small pip-job-action" data-job-id="<?php echo (int)$job->id; ?>" data-action="retry" data-confirm="<?php esc_attr_e('Are you sure you want to retry this job?', 'power-importer-pro'); ?>"><?php _e('Retry', 'power-importer-pro'); ?></button> <?php endif; ?>
                                <?php if ( in_array($job->status, ['completed', 'failed', 'cancelled']) ) : ?> <button class="button-link-delete pip-job-action" data-job-id="<?php echo (int)$job->id; ?>" data-action="delete" data-confirm="<?php esc_attr_e('Are you sure you want to delete this job and its logs? This cannot be undone.', 'power-importer-pro'); ?>"><span class="dashicons dashicons-trash" style="font-size: 1.2rem; vertical-align: middle;"></span></button> <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        <?php
    }

    private function render_log_details_page( $job_id ) {
        $job = pip_db()->get_job($job_id);
        if ( ! $job ) { echo '<div class="error"><p>' . __( 'Job not found.', 'power-importer-pro' ) . '</p></div>'; return; }
        $logs = pip_db()->get_logs_for_job($job_id);
        $back_link = remove_query_arg('view_log', admin_url('edit.php?post_type=product&page=power-importer-pro'));
        ?>
        <h2><?php printf( __( 'Log for Job #%d: %s', 'power-importer-pro' ), $job_id, esc_html($job->file_name) ); ?></h2>
        <p><a href="<?php echo esc_url($back_link); ?>">← <?php _e('Back to All Jobs', 'power-importer-pro'); ?></a></p>
        <div id="log-viewer" style="background: #fff; border: 1px solid #ccd0d4; padding: 15px; max-height: 600px; overflow-y: auto; font-family: monospace; white-space: pre-wrap; line-height: 1.6;">
            <?php if ( empty($logs) ) : ?>
                <p><?php _e('No log entries found for this job.', 'power-importer-pro'); ?></p>
            <?php else : ?>
                <?php foreach ( $logs as $log_entry ) : $level_class = 'log-level-' . strtolower(esc_attr($log_entry->log_level)); ?>
                    <div class="<?php echo $level_class; ?>"><strong>[<?php echo esc_html(date_i18n( 'Y-m-d H:i:s', strtotime($log_entry->log_timestamp))); ?>]</strong> <strong>[<?php echo esc_html($log_entry->log_level); ?>]</strong>: <?php echo esc_html($log_entry->message); ?></div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <style>.log-level-success { color: green; } .log-level-error { color: red; font-weight: bold; } .log-level-warning { color: orange; } .log-level-info { color: #555; }</style>
        <?php
    }

    private function get_upload_error_message( $error_code ) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE: return __( 'The uploaded file exceeds the upload_max_filesize directive in php.ini.', 'power-importer-pro' );
            case UPLOAD_ERR_FORM_SIZE: return __( 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form.', 'power-importer-pro' );
            case UPLOAD_ERR_PARTIAL: return __( 'The uploaded file was only partially uploaded.', 'power-importer-pro' );
            case UPLOAD_ERR_NO_FILE: return __( 'No file was uploaded.', 'power-importer-pro' );
            case UPLOAD_ERR_NO_TMP_DIR: return __( 'Missing a temporary folder.', 'power-importer-pro' );
            case UPLOAD_ERR_CANT_WRITE: return __( 'Failed to write file to disk.', 'power-importer-pro' );
            case UPLOAD_ERR_EXTENSION: return __( 'A PHP extension stopped the file upload.', 'power-importer-pro' );
            default: return __( 'Unknown upload error.', 'power-importer-pro' );
        }
    }
}