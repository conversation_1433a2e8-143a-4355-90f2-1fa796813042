<?php
/**
 * WooCommerce specific functions and customizations
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if (class_exists('WooCommerce')) {

    function flatsome_child_custom_product_layout() {
        if (is_product()) {

        }
    }
    add_action('wp', 'flatsome_child_custom_product_layout');

    function flatsome_child_custom_template_loader($template) {
        if (is_product()) {
            $custom_template = locate_template('woocommerce/single-product-custom.php');
            if (!empty($custom_template)) {
                do_action('flatsome_child_before_load_custom_product_template', $custom_template);
                return $custom_template;
            }
        }
        return $template;
    }
    add_filter('template_include', 'flatsome_child_custom_template_loader', 99); // High priority

    function flatsome_child_custom_features() {
        if (is_product()) {
            add_action('woocommerce_before_single_product_summary', function() {
                global $product;
                if ($product && $product->is_on_sale()) {
                    echo '<span class="custom-badge onsale">' . esc_html__('Sale!', 'flatsome-child') . '</span>';
                }
            }, 10);
        }
    }
    add_action('wp', 'flatsome_child_custom_features');

    function flatsome_child_fix_cart_url_fragments($fragments) {
        add_filter('woocommerce_get_cart_url', 'flatsome_child_force_cart_url', 10);
        add_filter('woocommerce_get_breadcrumb', 'flatsome_child_fix_breadcrumb_cart_url', 10);
        return $fragments;
    }
    add_filter('woocommerce_add_to_cart_fragments', 'flatsome_child_fix_cart_url_fragments');

    function flatsome_child_force_cart_url($url) {
        return wc_get_page_permalink('cart');
    }

    function flatsome_child_fix_breadcrumb_cart_url($crumbs) {
        if (is_checkout()) {
            foreach ($crumbs as $key => &$crumb) {
                if ($crumb[0] === __('Shopping Cart', 'woocommerce')) {
                    $crumbs[$key][1] = wc_get_page_permalink('cart');
                    $crumbs[$key][1] = apply_filters('flatsome_child_breadcrumb_cart_url', $crumbs[$key][1]);
                }
            }
        }
        return $crumbs;
    }

    function flatsome_child_fix_header_cart_url($url) {
        if (is_checkout()) {
            return wc_get_page_permalink('cart');
        }
        return $url;
    }
    add_filter('woocommerce_get_cart_url', 'flatsome_child_fix_header_cart_url', 20);

    /**
     * Extend Rank Math Product Schema with Missing Fields
     * Uses Rank Math's rank_math/json_ld filter to add shippingDetails and hasMerchantReturnPolicy
     */
    // Extend Rank Math Product Schema with missing fields
    add_filter('rank_math/json_ld', function($data, $jsonld) {
        if (!is_product()) {
            return $data;
        }

        global $product, $post;
        if (!$product || !$post) {
            return $data;
        }

        // Debug: Log the data structure
        error_log('Rank Math Data Structure: ' . print_r($data, true));

        // Get policy URLs - use hardcoded URLs for now to test
        $shipping_policy_url = home_url('/shipping-policy/');
        $return_policy_url = home_url('/refund_returns/');

        // Try to find actual shipping policy page
        $common_shipping_slugs = ['shipping-policy', 'shipping', 'delivery-policy', 'shipping-info'];
        foreach ($common_shipping_slugs as $slug) {
            $page = get_page_by_path($slug);
            if ($page && $page->post_status === 'publish') {
                $shipping_policy_url = get_permalink($page->ID);
                break;
            }
        }

        // Try to find actual return policy page
        $common_return_slugs = ['refund_returns', 'return-policy', 'returns', 'refund-policy'];
        foreach ($common_return_slugs as $slug) {
            $page = get_page_by_path($slug);
            if ($page && $page->post_status === 'publish') {
                $return_policy_url = get_permalink($page->ID);
                break;
            }
        }

        // Debug: Log policy URLs
        error_log('Shipping Policy URL: ' . $shipping_policy_url);
        error_log('Return Policy URL: ' . $return_policy_url);

        // Find Product schema in the data
        if (isset($data['@graph'])) {
            foreach ($data['@graph'] as $key => &$item) {
                if (isset($item['@type']) && $item['@type'] === 'Product') {
                    error_log('Found Product schema at key: ' . $key);

                    // Ensure offers exists and is properly structured
                    if (!isset($item['offers'])) {
                        $item['offers'] = array(
                            '@type' => 'Offer',
                            'price' => $product->get_price(),
                            'priceCurrency' => get_woocommerce_currency(),
                            'availability' => $product->is_in_stock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
                            'itemCondition' => 'https://schema.org/NewCondition',
                            'priceValidUntil' => date('Y-12-31', strtotime('+1 year')),
                            'url' => get_permalink($post->ID),
                            'seller' => array(
                                '@type' => 'Organization',
                                'name' => get_bloginfo('name'),
                                'url' => home_url()
                            )
                        );
                    }

                    // Add shipping details if we have a shipping policy page
                    if ($shipping_policy_url) {
                        $item['offers']['shippingDetails'] = array(
                            '@type' => 'OfferShippingDetails',
                            'shippingRate' => array(
                                '@type' => 'MonetaryAmount',
                                'value' => '0',
                                'currency' => get_woocommerce_currency()
                            ),
                            'shippingDestination' => array(
                                '@type' => 'DefinedRegion',
                                'addressCountry' => 'US'
                            ),
                            'deliveryTime' => array(
                                '@type' => 'ShippingDeliveryTime',
                                'handlingTime' => array(
                                    '@type' => 'QuantitativeValue',
                                    'minValue' => 1,
                                    'maxValue' => 2,
                                    'unitCode' => 'DAY'
                                ),
                                'transitTime' => array(
                                    '@type' => 'QuantitativeValue',
                                    'minValue' => 3,
                                    'maxValue' => 7,
                                    'unitCode' => 'DAY'
                                )
                            )
                        );
                        error_log('Added shippingDetails to offers');
                    }

                    // Add return policy if we have a return policy page
                    if ($return_policy_url) {
                        $item['offers']['hasMerchantReturnPolicy'] = array(
                            '@type' => 'MerchantReturnPolicy',
                            '@id' => $return_policy_url . '#return-policy',
                            'applicableCountry' => 'US',
                            'returnPolicyCategory' => 'https://schema.org/MerchantReturnFiniteReturnWindow',
                            'merchantReturnDays' => 30,
                            'returnMethod' => 'https://schema.org/ReturnByMail',
                            'returnFees' => 'https://schema.org/FreeReturn',
                            'url' => $return_policy_url
                        );
                        error_log('Added hasMerchantReturnPolicy to offers');
                    }

                    // Debug: Log the modified offers
                    error_log('Modified offers: ' . print_r($item['offers'], true));

                    break; // Found and modified the Product, exit loop
                }
            }
        }

        return $data;
    }, 99, 2);

    // Backup method: Use JavaScript to modify the schema after page load
    add_action('wp_footer', function() {
        if (!is_product()) {
            return;
        }
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Find Rank Math schema script
            const rankMathScript = document.querySelector('script.rank-math-schema');
            if (rankMathScript) {
                try {
                    const data = JSON.parse(rankMathScript.textContent);
                    let modified = false;

                    if (data['@graph']) {
                        data['@graph'].forEach(function(item) {
                            if (item['@type'] === 'Product' && item.offers) {
                                // Add shipping details if missing
                                if (!item.offers.shippingDetails) {
                                    item.offers.shippingDetails = {
                                        '@type': 'OfferShippingDetails',
                                        'shippingRate': {
                                            '@type': 'MonetaryAmount',
                                            'value': '0',
                                            'currency': 'USD'
                                        },
                                        'shippingDestination': {
                                            '@type': 'DefinedRegion',
                                            'addressCountry': 'US'
                                        },
                                        'deliveryTime': {
                                            '@type': 'ShippingDeliveryTime',
                                            'handlingTime': {
                                                '@type': 'QuantitativeValue',
                                                'minValue': 1,
                                                'maxValue': 2,
                                                'unitCode': 'DAY'
                                            },
                                            'transitTime': {
                                                '@type': 'QuantitativeValue',
                                                'minValue': 3,
                                                'maxValue': 7,
                                                'unitCode': 'DAY'
                                            }
                                        }
                                    };
                                    modified = true;
                                    console.log('Added shippingDetails via JavaScript');
                                }

                                // Add return policy if missing
                                if (!item.offers.hasMerchantReturnPolicy) {
                                    item.offers.hasMerchantReturnPolicy = {
                                        '@type': 'MerchantReturnPolicy',
                                        '@id': window.location.origin + '/refund_returns/#return-policy',
                                        'applicableCountry': 'US',
                                        'returnPolicyCategory': 'https://schema.org/MerchantReturnFiniteReturnWindow',
                                        'merchantReturnDays': 30,
                                        'returnMethod': 'https://schema.org/ReturnByMail',
                                        'returnFees': 'https://schema.org/FreeReturn',
                                        'url': window.location.origin + '/refund_returns/'
                                    };
                                    modified = true;
                                    console.log('Added hasMerchantReturnPolicy via JavaScript');
                                }
                            }
                        });
                    }

                    // Update the script content if modified
                    if (modified) {
                        rankMathScript.textContent = JSON.stringify(data);
                        console.log('Updated Rank Math schema with missing fields');
                    }
                } catch (e) {
                    console.error('Error modifying Rank Math schema:', e);
                }
            }
        });
        </script>
        <?php
    }, 999);

    // Remove our custom schema since we're extending Rank Math's instead
    // This approach prevents duplicate schemas and extends existing ones

}