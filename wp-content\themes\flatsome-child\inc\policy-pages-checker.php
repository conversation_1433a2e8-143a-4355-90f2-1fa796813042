<?php
/**
 * Policy Pages Configuration Checker
 * 用于检查和验证运输、退换货政策页面设置
 */

if (!defined('ABSPATH')) {
    exit;
}

// 只在管理员后台显示检查工具
if (is_admin()) {
    add_action('admin_menu', 'flatsome_child_add_policy_checker_menu');
    add_action('admin_init', 'flatsome_child_handle_policy_checker');
}

function flatsome_child_add_policy_checker_menu() {
    add_submenu_page(
        'woocommerce',
        '政策页面检查器',
        '政策页面检查器',
        'manage_options',
        'policy-pages-checker',
        'flatsome_child_policy_checker_page'
    );
}

function flatsome_child_policy_checker_page() {
    ?>
    <div class="wrap">
        <h1>政策页面配置检查器</h1>
        <p>此工具帮助检查运输和退换货政策页面的配置状态，确保结构化数据正确引用。</p>
        
        <?php
        // 检查 WooCommerce 设置
        $shipping_page_id = function_exists('wc_get_page_id') ? wc_get_page_id('shipping_policy') : 0;
        $return_page_id = function_exists('wc_get_page_id') ? wc_get_page_id('refund_returns') : 0;
        
        echo '<div class="notice notice-info"><p><strong>当前 WooCommerce 设置：</strong></p>';
        
        if ($shipping_page_id && $shipping_page_id > 0) {
            $shipping_url = get_permalink($shipping_page_id);
            echo '<p>✅ 运输政策页面: <a href="' . esc_url($shipping_url) . '" target="_blank">' . esc_url($shipping_url) . '</a></p>';
        } else {
            echo '<p>❌ 运输政策页面: 未设置</p>';
        }
        
        if ($return_page_id && $return_page_id > 0) {
            $return_url = get_permalink($return_page_id);
            echo '<p>✅ 退换货政策页面: <a href="' . esc_url($return_url) . '" target="_blank">' . esc_url($return_url) . '</a></p>';
        } else {
            echo '<p>❌ 退换货政策页面: 未设置</p>';
        }
        echo '</div>';
        
        // 检查常见页面 slug
        echo '<div class="notice notice-warning"><p><strong>检查常见政策页面：</strong></p>';
        
        $common_shipping_slugs = ['shipping-policy', 'shipping', 'delivery-policy', 'shipping-info'];
        $common_return_slugs = ['refund-returns', 'return-policy', 'returns', 'refund-policy'];
        
        echo '<p><strong>运输政策相关页面：</strong></p><ul>';
        foreach ($common_shipping_slugs as $slug) {
            $page = get_page_by_path($slug);
            if ($page) {
                $url = get_permalink($page->ID);
                echo '<li>✅ /' . $slug . '/ → <a href="' . esc_url($url) . '" target="_blank">' . esc_html($page->post_title) . '</a></li>';
            } else {
                echo '<li>❌ /' . $slug . '/ → 页面不存在</li>';
            }
        }
        echo '</ul>';
        
        echo '<p><strong>退换货政策相关页面：</strong></p><ul>';
        foreach ($common_return_slugs as $slug) {
            $page = get_page_by_path($slug);
            if ($page) {
                $url = get_permalink($page->ID);
                echo '<li>✅ /' . $slug . '/ → <a href="' . esc_url($url) . '" target="_blank">' . esc_html($page->post_title) . '</a></li>';
            } else {
                echo '<li>❌ /' . $slug . '/ → 页面不存在</li>';
            }
        }
        echo '</ul></div>';
        
        // 显示当前结构化数据会使用的 URL
        echo '<div class="notice notice-success"><p><strong>结构化数据将使用的 URL：</strong></p>';
        
        // 模拟获取 URL 的逻辑
        $final_shipping_url = '';
        $final_return_url = '';
        
        if ($shipping_page_id && $shipping_page_id > 0) {
            $final_shipping_url = get_permalink($shipping_page_id);
        } else {
            foreach ($common_shipping_slugs as $slug) {
                $page = get_page_by_path($slug);
                if ($page) {
                    $final_shipping_url = get_permalink($page->ID);
                    break;
                }
            }
        }
        
        if ($return_page_id && $return_page_id > 0) {
            $final_return_url = get_permalink($return_page_id);
        } else {
            foreach ($common_return_slugs as $slug) {
                $page = get_page_by_path($slug);
                if ($page) {
                    $final_return_url = get_permalink($page->ID);
                    break;
                }
            }
        }
        
        if (empty($final_shipping_url)) {
            $final_shipping_url = home_url('/shipping-policy/');
        }
        if (empty($final_return_url)) {
            $final_return_url = home_url('/refund-returns/');
        }
        
        echo '<p><strong>运输政策 URL:</strong> <code>' . esc_html($final_shipping_url) . '</code></p>';
        echo '<p><strong>退换货政策 URL:</strong> <code>' . esc_html($final_return_url) . '</code></p>';
        echo '</div>';
        ?>
        
        <div class="notice notice-info">
            <h3>📋 配置建议</h3>
            <h4>方法 1: 在 WooCommerce 中设置（推荐）</h4>
            <ol>
                <li>进入 <strong>WooCommerce → 设置 → 高级 → 页面设置</strong></li>
                <li>设置 <strong>运输政策页面</strong> 和 <strong>退换货政策页面</strong></li>
                <li>确保这些页面已发布且内容完整</li>
            </ol>
            
            <h4>方法 2: 创建标准 Slug 页面</h4>
            <p>如果不想在 WooCommerce 中设置，可以创建以下 slug 的页面：</p>
            <ul>
                <li><strong>运输政策:</strong> shipping-policy, shipping, delivery-policy, shipping-info</li>
                <li><strong>退换货政策:</strong> refund-returns, return-policy, returns, refund-policy</li>
            </ul>
            
            <h4>方法 3: 自定义 URL（高级）</h4>
            <p>如果你的页面使用其他 slug，可以在主题的 functions.php 中添加过滤器：</p>
            <pre><code>// 自定义政策页面 URL
add_filter('flatsome_child_shipping_policy_url', function() {
    return home_url('/your-custom-shipping-page/');
});

add_filter('flatsome_child_return_policy_url', function() {
    return home_url('/your-custom-return-page/');
});</code></pre>
        </div>
        
        <div class="notice notice-warning">
            <h3>⚠️ 重要提醒</h3>
            <ul>
                <li>确保政策页面内容完整，包含具体的运输时间、费用、退换货条件等</li>
                <li>页面必须是 <strong>已发布</strong> 状态，不能是草稿</li>
                <li>URL 必须可以正常访问，返回 200 状态码</li>
                <li>建议在页面中包含结构化数据相关的关键信息</li>
            </ul>
        </div>
        
        <div class="notice notice-success">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li>确保政策页面配置正确</li>
                <li>访问任意产品页面</li>
                <li>查看页面源代码，搜索 "shippingDetails" 和 "hasMerchantReturnPolicy"</li>
                <li>使用 <a href="https://search.google.com/test/rich-results" target="_blank">Google Rich Results Test</a> 测试</li>
                <li>检查是否还有 "未填写字段" 警告</li>
            </ol>
        </div>
    </div>
    <?php
}

function flatsome_child_handle_policy_checker() {
    // 处理任何表单提交或操作
}

// 添加过滤器支持自定义 URL
function flatsome_child_get_shipping_policy_url() {
    return apply_filters('flatsome_child_shipping_policy_url', '');
}

function flatsome_child_get_return_policy_url() {
    return apply_filters('flatsome_child_return_policy_url', '');
}
