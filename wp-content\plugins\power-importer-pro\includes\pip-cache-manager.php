<?php
/**
 * 缓存管理类 - 无Redis环境的优化方案
 * 
 * @package Power Importer Pro
 * @version 1.5.1-cache
 */

if ( ! defined( 'ABSPATH' ) ) exit;

class PIP_Cache_Manager {
    
    private static $instance = null;
    private $cache_groups = [
        'encoding_detection' => 600,      // 10分钟
        'product_attributes' => 3600,     // 1小时
        'category_hierarchy' => 7200,     // 2小时
        'import_progress' => 300          // 5分钟
    ];
    
    // 内存缓存
    private static $memory_cache = [];
    private static $memory_cache_ttl = [];
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 智能缓存获取/设置
     */
    public function smart_cache($key, $callback, $group = 'default') {
        $cache_key = "pip_{$group}_{$key}";
        
        // 先检查内存缓存
        $cached = $this->get_memory_cache($cache_key);
        if ($cached !== false) {
            return $cached;
        }
        
        // 再检查WordPress transient
        $cached = get_transient($cache_key);
        if ($cached === false) {
            $cached = $callback();
            $ttl = $this->cache_groups[$group] ?? 3600;
            
            // 同时存储到transient和内存缓存
            set_transient($cache_key, $cached, $ttl);
            $this->set_memory_cache($cache_key, $cached, $ttl);
        } else {
            // 从transient获取后也存储到内存缓存
            $ttl = $this->cache_groups[$group] ?? 3600;
            $this->set_memory_cache($cache_key, $cached, $ttl);
        }
        
        return $cached;
    }
    
    /**
     * 内存缓存设置
     */
    private function set_memory_cache($key, $value, $ttl = 3600) {
        self::$memory_cache[$key] = $value;
        self::$memory_cache_ttl[$key] = time() + $ttl;
    }
    
    /**
     * 内存缓存获取
     */
    private function get_memory_cache($key) {
        if (isset(self::$memory_cache[$key]) && isset(self::$memory_cache_ttl[$key])) {
            if (time() < self::$memory_cache_ttl[$key]) {
                return self::$memory_cache[$key];
            } else {
                // 清理过期缓存
                unset(self::$memory_cache[$key], self::$memory_cache_ttl[$key]);
            }
        }
        return false;
    }
    
    /**
     * 清理指定组的缓存
     */
    public function invalidate_group($group) {
        global $wpdb;
        
        // 清理transients
        $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
                '_transient_pip_' . $group . '_%'
            )
        );
        
        // 清理内存缓存
        foreach (self::$memory_cache as $key => $value) {
            if (strpos($key, "pip_{$group}_") === 0) {
                unset(self::$memory_cache[$key], self::$memory_cache_ttl[$key]);
            }
        }
    }
    
    /**
     * 清理所有缓存
     */
    public function clear_all_cache() {
        global $wpdb;
        
        // 清理所有插件相关的transients
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_pip_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_pip_%'");
        
        // 清理内存缓存
        self::$memory_cache = [];
        self::$memory_cache_ttl = [];
    }
    
    /**
     * 获取缓存统计信息
     */
    public function get_cache_stats() {
        global $wpdb;
        
        $transient_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE '_transient_pip_%'");
        $memory_count = count(self::$memory_cache);
        
        return [
            'transients' => $transient_count,
            'memory_cache' => $memory_count,
            'total' => $transient_count + $memory_count
        ];
    }
}