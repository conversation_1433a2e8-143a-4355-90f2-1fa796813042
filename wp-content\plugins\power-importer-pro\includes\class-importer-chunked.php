<?php
/**
 * 分块导入器类 - 解决大文件超时问题
 * 
 * @package Power Importer Pro
 * @version 1.5.1-chunked
 */

if ( ! defined( 'ABSPATH' ) ) exit;

class PIP_Chunked_Importer extends PIP_Importer {
    
    private $chunk_size = 20;           // 每块处理20行（更安全的数量）
    private $current_offset = 0;        // 当前处理位置
    private $max_execution_time = 90;   // 最大执行时间（秒）- 给图片处理更多时间
    private $start_time;                // 开始时间
    
    /**
     * 构造函数
     */
    public function __construct($csv_path, $job_id, $chunk_size = null) {
        parent::__construct($csv_path, $job_id);
        
        // 从WordPress设置或参数获取配置
        $this->chunk_size = $chunk_size ?: get_option('pip_chunk_size', 20);
        $this->max_execution_time = get_option('pip_max_execution_time', 90);
        $this->start_time = time();
        
        // 从数据库恢复上次处理位置
        $this->restore_processing_state();
    }
    
    /**
     * 分块运行 - 替代原来的run方法
     */
    public function run_chunk() {
        try {
            $this->log("--- 开始分块任务 #{$this->current_job_id} (偏移: {$this->current_offset}) ---", 'INFO');
            
            // 更新任务状态
            if ($this->current_offset === 0) {
                pip_db()->update_job($this->current_job_id, [
                    'status' => 'running', 
                    'started_at' => current_time('mysql', 1)
                ]);
            }

            add_filter( 'upload_dir', [ $this, 'custom_upload_dir' ] );

            if (!file_exists($this->csv_path)) {
                throw new Exception("CSV文件未找到于路径: " . $this->csv_path);
            }

            // 如果是第一次运行，计算总行数
            if ($this->current_offset === 0) {
                $total_rows = $this->count_csv_rows();
                pip_db()->update_job($this->current_job_id, ['total_rows' => $total_rows]);
                $this->log("文件总行数: {$total_rows}", 'INFO');
            }

            // 处理当前块
            $processed_count = $this->process_chunk();
            
            // 更新进度
            $this->current_offset += $processed_count;
            $this->row_count += $processed_count;
            
            pip_db()->update_job($this->current_job_id, [
                'processed_rows' => $this->row_count
            ]);
            
            // 保存处理状态
            $this->save_processing_state();
            
            // 检查是否完成
            if ($this->is_file_completed()) {
                $this->complete_import();
            } else {
                // 调度下一个块
                $this->schedule_next_chunk();
            }
            
        } catch (Exception $e) {
            $this->log("分块处理错误: " . $e->getMessage(), 'ERROR');
            pip_db()->update_job($this->current_job_id, [
                'status' => 'failed', 
                'finished_at' => current_time('mysql', 1)
            ]);
        }

        remove_filter( 'upload_dir', [ $this, 'custom_upload_dir' ] );
    }
    
    /**
     * 处理当前数据块
     */
    private function process_chunk() {
        $processed = 0;
        $line_number = 0;
        
        if (($handle = fopen($this->csv_path, "r")) !== FALSE) {
            // 跳过标题行
            $headers = array_map('trim', fgetcsv($handle));
            $line_number++;
            
            // 跳到当前偏移位置
            while ($line_number <= $this->current_offset && ($data = fgetcsv($handle)) !== FALSE) {
                $line_number++;
            }
            
            // 处理当前块的数据
            while ($processed < $this->chunk_size && 
                   ($data = fgetcsv($handle)) !== FALSE &&
                   !$this->is_time_limit_exceeded()) {
                
                $line_number++;
                
                if (count($data) !== count($headers)) {
                    $this->log("⚠️ [行号: {$line_number}] 列数与表头不匹配，已跳过。", 'WARNING');
                    $processed++;
                    continue;
                }
                
                $product_data = array_combine($headers, $data);
                $this->process_row($product_data);
                $processed++;
                
                // 每10行检查一次时间
                if ($processed % 10 === 0 && $this->is_time_limit_exceeded()) {
                    $this->log("⏰ 时间限制到达，提前结束当前块", 'INFO');
                    break;
                }
            }
            
            fclose($handle);
        }
        
        $this->log("✅ 当前块处理完成: {$processed} 行", 'INFO');
        return $processed;
    }
    
    /**
     * 计算CSV文件总行数
     */
    private function count_csv_rows() {
        $count = 0;
        if (($handle = fopen($this->csv_path, "r")) !== FALSE) {
            while (($data = fgetcsv($handle)) !== FALSE) {
                $count++;
            }
            fclose($handle);
        }
        return max(0, $count - 1); // 减去标题行
    }
    
    /**
     * 检查是否超过时间限制
     */
    private function is_time_limit_exceeded() {
        return (time() - $this->start_time) >= $this->max_execution_time;
    }
    
    /**
     * 检查文件是否处理完成
     */
    private function is_file_completed() {
        $job = pip_db()->get_job($this->current_job_id);
        return $job && $this->row_count >= $job->total_rows;
    }
    
    /**
     * 调度下一个处理块
     */
    private function schedule_next_chunk() {
        // 检查是否启用智能延迟（默认关闭）
        $smart_delay = get_option('pip_enable_smart_delay', false);
        
        if ($smart_delay && $this->should_apply_delay()) {
            // 只在服务器负载高时才延迟
            $delay = $this->get_smart_delay();
            $action_id = as_schedule_single_action(
                time() + $delay,
                'pip_process_chunk',
                [ $this->current_job_id ],
                'power-importer-pro-group'
            );
            $this->log("⏳ 检测到高负载，延迟 {$delay} 秒后处理下一块", 'INFO');
        } else {
            // 默认：立即调度下一个块，无延迟！
            $action_id = as_enqueue_async_action(
                'pip_process_chunk',
                [ $this->current_job_id ],
                'power-importer-pro-group'
            );
            $this->log("⚡ 已调度下一个块（无延迟）", 'INFO');
        }
        
        if (!$action_id) {
            $this->log("❌ 调度下一个块失败", 'ERROR');
        }
    }
    
    /**
     * 检查是否应该应用延迟
     */
    private function should_apply_delay() {
        // 检查服务器负载指标
        $processing_time = time() - $this->start_time;
        $memory_usage = memory_get_usage(true);
        $memory_limit = $this->parse_memory_limit();
        
        // 如果处理时间超过80%限制，或内存使用超过70%，则应用延迟
        return ($processing_time > ($this->max_execution_time * 0.8)) || 
               ($memory_usage > ($memory_limit * 0.7));
    }
    
    /**
     * 获取智能延迟时间
     */
    private function get_smart_delay() {
        // 根据当前Action Scheduler队列长度动态调整
        $pending_actions = as_get_scheduled_actions([
            'group' => 'power-importer-pro-group',
            'status' => 'pending'
        ], 'ids');
        
        $queue_length = count($pending_actions);
        
        if ($queue_length > 10) {
            return 3; // 队列较长时短延迟
        } else {
            return 1; // 队列正常时最小延迟
        }
    }
    
    /**
     * 解析内存限制
     */
    private function parse_memory_limit() {
        $memory_limit = ini_get('memory_limit');
        if (preg_match('/^(\d+)(.)$/', $memory_limit, $matches)) {
            if ($matches[2] == 'M') {
                return $matches[1] * 1024 * 1024;
            } else if ($matches[2] == 'K') {
                return $matches[1] * 1024;
            }
        }
        return 128 * 1024 * 1024; // 默认128MB
    }
    
    /**
     * 记录日志
     */
    private function log($message, $level = 'INFO') {
        pip_db()->add_log($this->current_job_id, $message, $level);
    }
    
    /**
     * 完成导入
     */
    private function complete_import() {
        $this->log("--- 分块导入数据处理完毕，开始收尾工作 ---", 'SUCCESS');
        
        // 🏁 执行完整的收尾工作（可在设置中控制）
        $enable_cleanup = get_option('pip_enable_post_import_cleanup', true);
        if ($enable_cleanup) {
            $this->perform_post_import_cleanup();
        } else {
            $this->log("⚠️ 收尾工作已禁用，跳过清理步骤", 'WARNING');
        }
        
        pip_db()->update_job($this->current_job_id, [
            'status' => 'completed',
            'finished_at' => current_time('mysql', 1),
            'processed_rows' => $this->row_count
        ]);
        
        // 刷新所有批量操作
        $this->flush_all_batch_operations();
        
        // 清理处理状态
        $this->clear_processing_state();
    }
    
    /**
     * 保存处理状态
     */
    private function save_processing_state() {
        update_option("pip_chunk_state_{$this->current_job_id}", [
            'offset' => $this->current_offset,
            'row_count' => $this->row_count,
            'variable_product_map' => $this->variable_product_map,
            'last_update' => time()
        ]);
    }
    
    /**
     * 恢复处理状态
     */
    private function restore_processing_state() {
        $state = get_option("pip_chunk_state_{$this->current_job_id}", false);
        
        if ($state && is_array($state)) {
            $this->current_offset = $state['offset'] ?? 0;
            $this->row_count = $state['row_count'] ?? 0;
            $this->variable_product_map = $state['variable_product_map'] ?? [];
            
            $this->log("📋 恢复处理状态: 偏移={$this->current_offset}, 已处理={$this->row_count}", 'INFO');
        }
    }
    
    /**
     * 清理处理状态
     */
    private function clear_processing_state() {
        delete_option("pip_chunk_state_{$this->current_job_id}");
    }
    
    /**
     * 原有的run方法保持兼容性（不建议用于大文件）
     */
    public function run() {
        $this->log("⚠️ 使用传统处理模式，建议切换到分块模式", 'WARNING');
        parent::run();
    }
} 