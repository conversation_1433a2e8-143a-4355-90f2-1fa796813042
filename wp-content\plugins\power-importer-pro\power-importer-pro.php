<?php
/**
 * Plugin Name:       Power Importer Pro
 * Plugin URI:        https://yourwebsite.com/
 * Description:       A professional tool to import WooCommerce products from a CSV file with background processing and detailed logging.
 * Version:           1.5.0 (Stable & Refactored)
 * Author:            Your Name
 * Author URI:        https://yourwebsite.com/
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       power-importer-pro
 * Domain Path:       /languages
 */

// 强制使用直接文件I/O，解决权限问题
if ( ! defined('FS_METHOD') ) {
    define('FS_METHOD', 'direct');
}

// 防止直接访问文件
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// 定义插件常量
define( 'PIP_VERSION', '1.5.0' );
define( 'PIP_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'PIP_UPLOAD_DIR_NAME', 'power-importer-pro-file' );

/**
 * 加载插件的所有文件
 */
require_once PIP_PLUGIN_DIR . 'includes/functions.php';
require_once PIP_PLUGIN_DIR . 'includes/class-database.php';
require_once PIP_PLUGIN_DIR . 'includes/class-importer.php';
require_once PIP_PLUGIN_DIR . 'includes/class-importer-chunked.php';
require_once PIP_PLUGIN_DIR . 'includes/class-manual-cleanup.php';
require_once PIP_PLUGIN_DIR . 'includes/class-auto-retry.php';
require_once PIP_PLUGIN_DIR . 'includes/pip-config-helper.php';
require_once PIP_PLUGIN_DIR . 'includes/pip-cache-manager.php';
require_once PIP_PLUGIN_DIR . 'includes/class-admin-page.php';

/**
 * 初始化插件和所有钩子
 */
function pip_init() {
    // 检查依赖项
    if ( ! function_exists('as_enqueue_async_action') ) {
        add_action( 'admin_notices', 'pip_show_dependencies_notice' );
        return;
    }
    
    // 初始化后台页面
    if ( is_admin() ) {
        new PIP_Admin_Page();
    }
    
    // 注册后台任务钩子
    add_action( 'pip_process_import_file', 'pip_run_import_task_callback', 10, 1 );
    // 注册分块处理钩子
    add_action( 'pip_process_chunk', 'pip_process_chunk_callback', 10, 1 );

    // 注册所有AJAX钩子
    add_action( 'wp_ajax_pip_get_jobs_table', 'pip_ajax_get_jobs_table_callback' );
    add_action( 'wp_ajax_pip_job_action', 'pip_ajax_handle_job_action_callback' );
    add_action( 'wp_ajax_pip_clear_jobs', 'pip_ajax_handle_clear_jobs_callback' );
    add_action( 'wp_ajax_pip_manual_cleanup', 'pip_ajax_handle_manual_cleanup_callback' );
}
add_action( 'plugins_loaded', 'pip_init' );

/**
 * 插件激活时运行的函数
 */
function pip_plugin_activation() {
    // 1. 创建上传目录
    $upload_dir = wp_upload_dir();
    $pip_dir = $upload_dir['basedir'] . '/' . PIP_UPLOAD_DIR_NAME;
    if ( ! file_exists( $pip_dir ) ) {
        wp_mkdir_p( $pip_dir );
        if ( ! file_exists($pip_dir . '/.htaccess') ) { file_put_contents($pip_dir . '/.htaccess', 'deny from all'); }
        if ( ! file_exists($pip_dir . '/index.html') ) { file_put_contents($pip_dir . '/index.html', '<!-- Silence is golden. -->'); }
    }
    
    // 2. 创建或更新数据库表
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );

    // 【SQL 修复】修正了所有默认值错误
    $jobs_table_name = $wpdb->prefix . 'pip_import_jobs';
    $sql_jobs = "CREATE TABLE $jobs_table_name (
        id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        file_name VARCHAR(255) NOT NULL,
        file_path TEXT NOT NULL,
        total_rows INT(11) UNSIGNED NOT NULL DEFAULT 0,
        processed_rows INT(11) UNSIGNED NOT NULL DEFAULT 0,
        retry_count INT(11) UNSIGNED NOT NULL DEFAULT 0,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        started_at DATETIME NULL DEFAULT NULL,
        finished_at DATETIME NULL DEFAULT NULL,
        created_at DATETIME NOT NULL,
        PRIMARY KEY  (id),
        KEY status (status)
    ) $charset_collate;";
    dbDelta( $sql_jobs );

    $logs_table_name = $wpdb->prefix . 'pip_import_logs';
    $sql_logs = "CREATE TABLE $logs_table_name (
        log_id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        job_id BIGINT(20) UNSIGNED NOT NULL,
        log_level VARCHAR(20) NOT NULL DEFAULT 'INFO',
        message TEXT NOT NULL,
        log_timestamp DATETIME NOT NULL,
        PRIMARY KEY  (log_id),
        KEY job_id (job_id),
        KEY log_level (log_level)
    ) $charset_collate;";
    dbDelta( $sql_logs );
    
    // 添加必要的数据库索引优化
    pip_add_database_indexes();
}
register_activation_hook( __FILE__, 'pip_plugin_activation' );

/**
 * 添加数据库索引优化
 */
function pip_add_database_indexes() {
    global $wpdb;
    
    // 检查索引是否已存在，避免重复添加
    $indexes_to_add = [
        // 插件表索引
        'pip_import_jobs' => [
            'idx_status_created' => "ALTER TABLE {$wpdb->prefix}pip_import_jobs ADD INDEX idx_status_created (status, created_at)",
            'idx_file_path' => "ALTER TABLE {$wpdb->prefix}pip_import_jobs ADD INDEX idx_file_path (file_path(255))"
        ]
    ];
    
    foreach ($indexes_to_add as $table => $indexes) {
        foreach ($indexes as $index_name => $sql) {
            // 检查索引是否已存在
            $table_name = $wpdb->prefix . $table;
            $existing_indexes = $wpdb->get_results($wpdb->prepare("SHOW INDEX FROM {$table_name} WHERE Key_name = %s", $index_name));
            if (empty($existing_indexes)) {
                $wpdb->query($sql);
            }
        }
    }
}

/**
 * 当依赖项缺失时显示提示
 */
function pip_show_dependencies_notice() {
    echo '<div class="error"><p>' . __( 'Power Importer Pro requires WooCommerce to be activated to function.', 'power-importer-pro' ) . '</p></div>';
}