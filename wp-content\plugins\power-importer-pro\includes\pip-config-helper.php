<?php
/**
 * Power Importer Pro 配置助手
 * 
 * @package Power Importer Pro
 * @version 1.5.1
 */

if ( ! defined( 'ABSPATH' ) ) exit;

class PIP_Config_Helper {
    
    /**
     * 初始化配置
     */
    public static function init() {
        add_action('admin_init', [__CLASS__, 'register_settings']);
        add_filter('action_scheduler_queue_runner_concurrent_batches', [__CLASS__, 'set_concurrent_batches']);
        add_filter('action_scheduler_queue_runner_batch_size', [__CLASS__, 'set_batch_size']);
    }
    
    /**
     * 注册WordPress设置
     */
    public static function register_settings() {
        // 注册分块处理设置
        register_setting('pip_settings', 'pip_chunk_size', [
            'type' => 'integer',
            'default' => 20,
            'sanitize_callback' => function($value) {
                return max(5, min(100, intval($value))); // 限制在5-100之间
            }
        ]);
        
        register_setting('pip_settings', 'pip_max_execution_time', [
            'type' => 'integer', 
            'default' => 90,
            'sanitize_callback' => function($value) {
                return max(30, min(300, intval($value))); // 限制在30-300秒之间
            }
        ]);
        
        register_setting('pip_settings', 'pip_concurrent_jobs', [
            'type' => 'integer',
            'default' => 1,  // 默认改为1个并发
            'sanitize_callback' => function($value) {
                return max(1, min(10, intval($value))); // 限制在1-10之间
            }
        ]);
        
        register_setting('pip_settings', 'pip_large_file_threshold', [
            'type' => 'integer',
            'default' => 3,  // 修改默认阈值为3MB
            'sanitize_callback' => function($value) {
                return max(1, min(50, intval($value))); // 限制在1-50MB之间
            }
        ]);
        
        register_setting('pip_settings', 'pip_enable_smart_delay', [
            'type' => 'boolean',
            'default' => true, // 默认开启智能延迟
        ]);
        
        // 图片验证模式
        register_setting('pip_settings', 'pip_strict_image_mode', [
            'type' => 'boolean',
            'default' => true, // 默认开启图片严格验证
        ]);
        
        // 收尾工作控制
        register_setting('pip_settings', 'pip_auto_cleanup_logs', [
            'type' => 'boolean',
            'default' => true, // 默认开启自动清理日志
        ]);
        
        register_setting('pip_settings', 'pip_enable_post_import_cleanup', [
            'type' => 'boolean',
            'default' => true, // 默认开启收尾工作
        ]);
        
        // 自动重试配置
        register_setting('pip_settings', 'pip_enable_auto_retry', [
            'type' => 'boolean',
            'default' => true, // 默认开启自动重试
        ]);
    }
    
    /**
     * 设置Action Scheduler并发批次数
     */
    public static function set_concurrent_batches($batches) {
        return get_option('pip_concurrent_jobs', 1);  // 默认改为1
    }
    
    /**
     * 设置批次大小
     */
    public static function set_batch_size($size) {
        return 1; // 每个批次处理1个任务，确保精确控制
    }
    
    /**
     * 获取配置值
     */
    public static function get_config($key, $default = null) {
        $configs = [
            'chunk_size' => get_option('pip_chunk_size', 20),
            'max_execution_time' => get_option('pip_max_execution_time', 90),
            'concurrent_jobs' => get_option('pip_concurrent_jobs', 1),  // 默认改为1
            'large_file_threshold' => get_option('pip_large_file_threshold', 3),  // 修改默认为3MB
        ];
        
        return isset($configs[$key]) ? $configs[$key] : $default;
    }
    
    /**
     * 获取推荐配置基于服务器性能
     */
    public static function get_recommended_config() {
        $memory_limit = self::parse_size(ini_get('memory_limit'));
        $max_execution_time = ini_get('max_execution_time');
        
        // 基于服务器性能的推荐配置
        if ($memory_limit >= 512 * 1024 * 1024) { // 512MB+
            return [
                'chunk_size' => 30,
                'max_execution_time' => 120,
                'concurrent_jobs' => 5,
                'performance_level' => 'high'
            ];
        } elseif ($memory_limit >= 256 * 1024 * 1024) { // 256MB+
            return [
                'chunk_size' => 20,
                'max_execution_time' => 90,
                'concurrent_jobs' => 1,  // 改为1
                'performance_level' => 'medium'
            ];
        } else { // 低性能服务器
            return [
                'chunk_size' => 10,
                'max_execution_time' => 60,
                'concurrent_jobs' => 1,  // 改为1
                'performance_level' => 'low'
            ];
        }
    }
    
    /**
     * 解析PHP内存限制
     */
    private static function parse_size($size) {
        $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
        $size = preg_replace('/[^0-9\.]/', '', $size);
        
        if ($unit) {
            return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
        }
        
        return round($size);
    }
    
    /**
     * 检查是否应该使用分块处理
     */
    public static function should_use_chunked_processing($file_path) {
        if (!file_exists($file_path)) {
            return false;
        }
        
        $file_size_mb = filesize($file_path) / (1024 * 1024);
        $threshold = self::get_config('large_file_threshold', 3);  // 修改默认为3MB
        
        return $file_size_mb > $threshold;
    }
}

// 初始化配置助手
PIP_Config_Helper::init(); 